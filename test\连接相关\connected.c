/**
 * SFS控制器 - 主连接函数
 *
 * 功能描述：
 * 这是SmartFoxServer连接的核心函数，负责建立与游戏服务器的网络连接。
 * 该函数处理连接的完整生命周期，包括：
 * 1. 检查当前连接状态
 * 2. 配置连接参数
 * 3. 创建SmartFox实例
 * 4. 注册事件监听器
 * 5. 设置错误代码映射
 * 6. 发起实际连接
 * 7. 处理各种连接状态和错误情况
 *
 * @param a1 SFS控制器实例指针，包含连接配置和状态信息
 * @param a2 连接回调函数指针，连接完成后调用
 * @param a3 错误回调函数指针，连接失败时调用
 * @param a4 是否显示UI消息的标志位（1=显示，0=不显示）
 * @return __int64 连接结果：1表示连接成功启动，0表示连接失败
 *
 * 事件监听器说明：
 * - EOEDAOGGJFN: 连接结果事件处理器
 * - PNJEOCPLAGP: 连接丢失事件处理器
 * - EIEACBBEFCI: 登录结果事件处理器
 * - IENIOAABKAL: 连接错误事件处理器
 * - FFIGKBKIKJH: 扩展响应事件处理器
 * - EHJAGHMDHIC: 用户变量更新事件处理器
 *
 * 错误消息说明：
 * - StringLiteral_5412: "已断开连接"
 * - StringLiteral_5413: "正在连接中"
 * - StringLiteral_5414: "连接失败"
 * - StringLiteral_4778: "正在连接服务器..."
 * - StringLiteral_4789: "连接超时"
 */
__int64 __fastcall SFSController__Connect(__int64 a1, __int64 a2, __int64 a3, char a4)
{
  __int64 v8; // x0 - SmartFox实例指针
  __int64 instance; // x0 - 通用实例指针（多用途）
  __int64 v10; // x0 - SmartFox实例指针（重新获取）
  __int64 v11; // x8 - UI管理器指针
  __int64 *v12; // x8 - 消息字符串指针
  __int64 v13; // x8 - UI管理器指针
  int v14; // w1 - 反作弊布尔值密钥
  __int64 v15; // x20 - 配置数据对象指针
  _QWORD *SFSController_TypeInfo; // x0 - SFS控制器类型信息
  __int64 v17; // x21 - 服务器地址字符串
  __int64 v18; // x0 - 字符串比较结果
  _QWORD *v19; // x8 - 服务器地址指针
  _QWORD *SFSController_TypeInfo_1; // x0 - SFS控制器类型信息（重新获取）
  __int64 v21; // x21 - SmartFox实例指针
  _QWORD *Sfs2X.Core.SFSEvent_TypeInfo; // x0 - SFS事件类型信息
  __int64 v23; // x21 - SmartFox实例指针（用于事件注册）
  __int64 v24; // x22 - 连接事件类型
  __int64 v25; // x23 - 事件监听器委托
  __int64 v26; // x22 - SmartFox实例指针
  __int64 v27; // x21 - 连接丢失事件类型
  __int64 v28; // x23 - 事件监听器委托
  __int64 v29; // x22 - SmartFox实例指针
  __int64 v30; // x21 - 登录事件类型
  __int64 v31; // x23 - 事件监听器委托
  __int64 v32; // x22 - SmartFox实例指针
  __int64 v33; // x21 - 连接错误事件类型
  __int64 v34; // x23 - 事件监听器委托
  __int64 v35; // x22 - SmartFox实例指针
  __int64 v36; // x21 - 扩展响应事件类型
  __int64 v37; // x23 - 事件监听器委托
  __int64 v38; // x22 - SmartFox实例指针
  __int64 v39; // x21 - 用户变量事件类型
  __int64 v40; // x23 - 事件监听器委托
  __int64 instance_1; // x0 - SmartFox实例指针（状态检查用）
  __int64 v43; // x8 - UI管理器指针
  __int64 v44; // x8 - UI管理器指针
  __int64 v45; // x8 - UI管理器指针

  // 静态初始化检查 - 确保所有需要的类型信息和方法指针只初始化一次
  if ( (byte_490BBBC & 1) == 0 )
  {
    // 初始化SFS相关类型信息
    sub_184E408(&Sfs2X_Util_ConfigData_TypeInfo);  // SFS配置数据类型
    sub_184E408(&Sfs2X_Core_EventListenerDelegate_TypeInfo);  // 事件监听器委托类型
    sub_184E408(&GameController_TypeInfo);  // 游戏控制器类型

    // 初始化事件处理方法指针
    sub_184E408(&Method_SFSController_EHJAGHMDHIC__);  // 用户变量更新事件处理器
    sub_184E408(&Method_SFSController_EIEACBBEFCI__);  // 登录结果事件处理器
    sub_184E408(&Method_SFSController_EOEDAOGGJFN__);  // 连接结果事件处理器
    sub_184E408(&Method_SFSController_FFIGKBKIKJH__);  // 扩展响应事件处理器
    sub_184E408(&Method_SFSController_IENIOAABKAL__);  // 连接错误事件处理器
    sub_184E408(&Method_SFSController_PNJEOCPLAGP__);  // 连接丢失事件处理器

    // 初始化其他必要的类型信息
    sub_184E408(&SFSController_TypeInfo);  // SFS控制器类型
    sub_184E408(&Sfs2X_Util_SFSErrorCodes_TypeInfo);  // SFS错误代码类型
    sub_184E408(&Sfs2X_Core_SFSEvent_TypeInfo);  // SFS核心事件类型
    sub_184E408(&Sfs2X_SmartFox_TypeInfo);  // SmartFox主类型

    // 初始化字符串字面量
    sub_184E408(&StringLiteral_18958);  // 通用连接错误消息
    sub_184E408(&StringLiteral_5412);   // "已断开连接"
    sub_184E408(&StringLiteral_5413);   // "正在连接中"
    sub_184E408(&StringLiteral_18956);  // 特定错误消息6
    sub_184E408(&StringLiteral_5331);   // 协程标识符
    sub_184E408(&StringLiteral_4787);   // 连接失败消息
    sub_184E408(&StringLiteral_18957);  // 特定错误消息7
    sub_184E408(&StringLiteral_4778);   // "正在连接服务器..."
    sub_184E408(&StringLiteral_4789);   // "连接超时"
    sub_184E408(&StringLiteral_1);      // 空字符串或默认值
    sub_184E408(&StringLiteral_5414);   // "连接失败"
    byte_490BBBC = 1;  // 标记已初始化
  }
  // === 第一步：检查当前连接状态 ===
  v8 = *(_QWORD *)(a1 + 64);  // 获取SmartFox实例
  if ( !v8 )
    goto LABEL_16;  // 如果实例不存在，跳转到连接初始化

  // 检查是否已经连接
  if ( (Sfs2X_SmartFox__get_IsConnected(v8, 0LL) & 1) == 0 )
  {
    instance = *(_QWORD *)(a1 + 64);
    if ( !instance )
      goto LABEL_70;  // 实例获取失败，跳转到错误处理

    // 检查是否正在连接中
    if ( (Sfs2X_SmartFox__get_IsConnecting(instance, 0LL) & 1) == 0 )
    {
LABEL_16:
      // === 第二步：开始新的连接流程 ===
      // 如果需要显示UI消息，显示"正在连接服务器..."
      if ( (a4 & 1) != 0 )
      {
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        instance = GameController__get_instance(0LL);
        if ( !instance )
          goto LABEL_70;
        v13 = *(_QWORD *)(instance + 96);  // 获取UI管理器
        if ( !v13 )
          goto LABEL_70;
        instance = *(_QWORD *)(v13 + 160);  // 获取UI消息组件
        if ( !instance )
          goto LABEL_70;
        UI_Message__Show(instance, StringLiteral_4778, 1LL, 0LL);  // 显示"正在连接服务器..."
      }

      // === 第三步：保存连接参数和配置 ===
      // 保存UI显示标志（使用反作弊加密）
      *(_QWORD *)(a1 + 752) = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(a4 & 1, 0LL);
      *(_DWORD *)(a1 + 760) = v14;  // 保存加密密钥
      *(_QWORD *)(a1 + 120) = a2;   // 保存连接回调函数

      // === 第四步：创建和配置连接数据 ===
      // 创建SFS配置数据对象
      v15 = sub_184E624(Sfs2X_Util_ConfigData_TypeInfo);
      Sfs2X_Util_ConfigData___ctor(v15, 0LL);

      // === 第五步：确定服务器地址 ===
      SFSController_TypeInfo = SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo = SFSController_TypeInfo;
      }

      // 获取静态配置的服务器地址
      v17 = *(_QWORD *)(SFSController_TypeInfo[23] + 8LL);
      v18 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit(StringLiteral_1, 0LL);

      // 检查是否使用默认服务器地址
      if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Equality(v17, v18, 0LL) & 1) != 0 )
      {
        // 使用实例配置的服务器地址
        v19 = (_QWORD *)(a1 + 72);  // 偏移72字节处存储服务器地址
      }
      else
      {
        // 使用静态配置的服务器地址
        SFSController_TypeInfo_1 = SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_1 = SFSController_TypeInfo;
        }
        v19 = (_QWORD *)(SFSController_TypeInfo_1[23] + 8LL);  // 静态服务器地址
      }

      // 解密服务器地址并设置到配置中
      instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(*v19, 0LL);
      if ( v15 )
      {
        *(_QWORD *)(v15 + 16) = instance;  // 设置服务器地址
        *(_DWORD *)(v15 + 24) = *(_DWORD *)(a1 + 80);  // 设置服务器端口
        *(_QWORD *)(v15 + 48) = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                  *(_QWORD *)(a1 + 88),  // 解密区域名称
                                  0LL);

        // === 第六步：创建SmartFox实例 ===
        v21 = sub_184E624(Sfs2X_SmartFox_TypeInfo);
        instance = Sfs2X_SmartFox___ctor(v21, 0LL);
        *(_QWORD *)(a1 + 64) = v21;  // 保存SmartFox实例
        if ( v21 )
        {
          Sfs2X_SmartFox__set_ThreadSafeMode(v21, 1LL, 0LL);
          instance = *(_QWORD *)(a1 + 64);
          if ( instance )
          {
            Sfs2X_SmartFox__RemoveAllEventListeners(instance, 0LL);
            Sfs2X.Core.SFSEvent_TypeInfo = Sfs2X_Core_SFSEvent_TypeInfo;
            v23 = *(_QWORD *)(a1 + 64);
            if ( !*((_DWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(Sfs2X_Core_SFSEvent_TypeInfo);
              Sfs2X.Core.SFSEvent_TypeInfo = Sfs2X_Core_SFSEvent_TypeInfo;
            }
            v24 = *(_QWORD *)(Sfs2X.Core.SFSEvent_TypeInfo[23] + 16LL);
            v25 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
            instance = Sfs2X_Core_EventListenerDelegate___ctor(v25, a1, Method_SFSController_EOEDAOGGJFN__, 0LL);
            if ( v23 )
            {
              Sfs2X_SmartFox__AddEventListener(v23, v24, v25, 0LL);
              v26 = *(_QWORD *)(a1 + 64);
              v27 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 40LL);
              v28 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
              instance = Sfs2X_Core_EventListenerDelegate___ctor(v28, a1, Method_SFSController_PNJEOCPLAGP__, 0LL);
              if ( v26 )
              {
                Sfs2X_SmartFox__AddEventListener(v26, v27, v28, 0LL);
                v29 = *(_QWORD *)(a1 + 64);
                v30 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 88LL);
                v31 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                instance = Sfs2X_Core_EventListenerDelegate___ctor(v31, a1, Method_SFSController_EIEACBBEFCI__, 0LL);
                if ( v29 )
                {
                  Sfs2X_SmartFox__AddEventListener(v29, v30, v31, 0LL);
                  v32 = *(_QWORD *)(a1 + 64);
                  v33 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 96LL);
                  v34 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                  instance = Sfs2X_Core_EventListenerDelegate___ctor(v34, a1, Method_SFSController_IENIOAABKAL__, 0LL);
                  if ( v32 )
                  {
                    Sfs2X_SmartFox__AddEventListener(v32, v33, v34, 0LL);
                    v35 = *(_QWORD *)(a1 + 64);
                    v36 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 216LL);
                    v37 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                    instance = Sfs2X_Core_EventListenerDelegate___ctor(v37, a1, Method_SFSController_FFIGKBKIKJH__, 0LL);
                    if ( v35 )
                    {
                      Sfs2X_SmartFox__AddEventListener(v35, v36, v37, 0LL);
                      v38 = *(_QWORD *)(a1 + 64);
                      v39 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 408LL);
                      v40 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                      instance = Sfs2X_Core_EventListenerDelegate___ctor(
                                   v40,
                                   a1,
                                   Method_SFSController_EHJAGHMDHIC__,
                                   0LL);
                      if ( v38 )
                      {
                        Sfs2X_SmartFox__AddEventListener(v38, v39, v40, 0LL);
                        if ( !*((_DWORD *)Sfs2X_Util_SFSErrorCodes_TypeInfo + 56) )
                          j_il2cpp_runtime_class_init_0(Sfs2X_Util_SFSErrorCodes_TypeInfo);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(6LL, StringLiteral_18956, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(7LL, StringLiteral_18957, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(0LL, StringLiteral_18958, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(10LL, StringLiteral_18957, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(28LL, StringLiteral_4787, 0LL);
                        *(_BYTE *)(a1 + 792) = 1;
                        MovementEffects_Timing__KillCoroutines_26616548(StringLiteral_5331, 0LL);
                        instance = *(_QWORD *)(a1 + 64);
                        if ( instance )
                        {
                          Sfs2X_SmartFox__Connect_63861312(instance, v15, 0LL);
                          return 1LL;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
LABEL_70:
      sub_184E634(instance);
    }
  }
  v10 = *(_QWORD *)(a1 + 64);
  if ( !v10 )
  {
LABEL_54:
    SFSController__DNIBMLBAGDM(a1, 1LL);
    if ( (a4 & 1) == 0 )
      goto LABEL_62;
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    instance = GameController__get_instance(0LL);
    if ( instance )
    {
      v44 = *(_QWORD *)(instance + 96);
      if ( v44 )
      {
        instance = *(_QWORD *)(v44 + 160);
        if ( instance )
        {
          v12 = &StringLiteral_5414;
          goto LABEL_61;
        }
      }
    }
    goto LABEL_70;
  }
  if ( (Sfs2X_SmartFox__get_IsConnected(v10, 0LL) & 1) == 0 )
  {
    instance_1 = *(_QWORD *)(a1 + 64);
    if ( instance_1 && (Sfs2X_SmartFox__get_IsConnecting(instance_1, 0LL) & 1) != 0 )
    {
      SFSController__DNIBMLBAGDM(a1, 1LL);
      if ( (a4 & 1) == 0 )
        goto LABEL_62;
      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
      instance = GameController__get_instance(0LL);
      if ( instance )
      {
        v43 = *(_QWORD *)(instance + 96);
        if ( v43 )
        {
          instance = *(_QWORD *)(v43 + 160);
          if ( instance )
          {
            v12 = &StringLiteral_5413;
            goto LABEL_61;
          }
        }
      }
      goto LABEL_70;
    }
    goto LABEL_54;
  }
  SFSController__Disconnect(a1);
  if ( (a4 & 1) == 0 )
    goto LABEL_62;
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_70;
  v11 = *(_QWORD *)(instance + 96);
  if ( !v11 )
    goto LABEL_70;
  instance = *(_QWORD *)(v11 + 160);
  if ( !instance )
    goto LABEL_70;
  v12 = &StringLiteral_5412;
LABEL_61:
  UI_Message__Show(instance, *v12, 1LL, 0LL);
LABEL_62:
  if ( a3 )
    (*(void (__fastcall **)(_QWORD, _QWORD))(a3 + 24))(*(_QWORD *)(a3 + 64), *(_QWORD *)(a3 + 40));
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_70;
  v45 = *(_QWORD *)(instance + 96);
  if ( !v45 )
    goto LABEL_70;
  instance = *(_QWORD *)(v45 + 160);
  if ( !instance )
    goto LABEL_70;
  UI_Message__Show(instance, StringLiteral_4789, 1LL, 0LL);
  return 0LL;
}