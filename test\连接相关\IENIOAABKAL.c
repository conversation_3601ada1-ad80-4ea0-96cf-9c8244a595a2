/**
 * SFS控制器 - 连接错误处理函数
 *
 * 功能描述：
 * 这个函数专门处理SmartFoxServer连接过程中发生的各种错误情况。
 * 当连接失败、超时或其他网络问题发生时，此函数会被调用来：
 * 1. 显示相应的错误消息给用户
 * 2. 从服务器事件中提取具体的错误信息
 * 3. 断开现有连接并清理资源
 * 4. 设置网络错误状态
 * 5. 执行错误回调函数
 *
 * @param a1 SFS控制器实例指针，包含连接状态和配置信息
 * @param a2 错误事件对象，包含服务器返回的错误详情
 * @return __int64 操作结果状态码
 *
 * 错误处理流程：
 * 1. 检查是否需要显示通用错误消息
 * 2. 从事件参数中提取具体错误信息
 * 3. 显示详细错误消息给用户
 * 4. 断开连接并清理资源
 * 5. 更新网络状态并触发回调
 */
__int64 __fastcall SFSController__IENIOAABKAL(__int64 a1, __int64 a2)
{
  _QWORD *instance; // x0 - GameController实例指针
  __int64 v5; // x8 - UI管理器指针
  __int64 v6; // x8 - UI管理器指针（重新获取）
  __int64 v7; // x21 - UI消息组件指针
  __int64 v8; // x8 - 事件参数对象类型信息
  _QWORD *instance_1; // x20 - 事件参数字典指针
  __int64 v10; // x9 - 字段数量
  __int64 StringLiteral_15595; // x22 - 错误消息键名字符串
  int *v12; // x10 - 字段数组指针
  __int64 v13; // x0 - 字典查找方法指针
  __int64 v14; // x0 - SmartFox实例指针
  __int64 result; // x0 - 函数返回值
  __int64 v16; // x8 - 错误回调函数指针
  __int64 v17; // x0 - 错误信息（类型不匹配时）

  // 静态初始化检查 - 确保所有需要的类型信息只初始化一次
  if ( (byte_490BC22 & 1) == 0 )
  {
    sub_184E408(&GameController_TypeInfo);  // 游戏控制器类型信息
    sub_184E408(&System_Collections_Generic_IDictionary_string__object__TypeInfo);  // 字典类型信息
    sub_184E408(&string_TypeInfo);  // 字符串类型信息
    sub_184E408(&StringLiteral_15595);  // 错误消息键名
    sub_184E408(&StringLiteral_4781);  // 通用错误消息文本
    byte_490BC22 = 1;  // 标记已初始化
  }

  // 检查是否需要显示通用错误消息（通过反作弊系统的布尔值判断）
  if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
          *(_QWORD *)(a1 + 752),  // 反作弊布尔值数据
          *(unsigned int *)(a1 + 760),  // 反作弊布尔值密钥
          0LL) & 1) != 0 )
  {
    // 获取GameController实例并显示通用错误消息
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    instance = (_QWORD *)GameController__get_instance(0LL);
    if ( !instance )
      goto LABEL_33;  // 实例获取失败，跳转到错误处理

    v5 = instance[12];  // 获取UI管理器
    if ( !v5 )
      goto LABEL_33;

    instance = *(_QWORD **)(v5 + 160);  // 获取UI消息组件
    if ( !instance )
      goto LABEL_33;

    // 显示通用错误消息
    UI_Message__Show(instance, StringLiteral_4781, 1LL, 0LL);
  }

  // === 从事件中提取具体错误信息 ===
  // 重新获取GameController实例
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = (_QWORD *)GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_33;

  v6 = instance[12];  // 获取UI管理器
  if ( !v6 )
    goto LABEL_33;

  if ( !a2 )  // 检查事件对象是否有效
    goto LABEL_33;

  v7 = *(_QWORD *)(v6 + 160);  // 保存UI消息组件指针

  // 从事件对象中获取参数字典
  instance = (_QWORD *)Sfs2X_Core_BaseEvent__get_Params(a2, 0LL);
  if ( !instance )
    goto LABEL_33;

  // 解析参数字典结构
  v8 = *instance;  // 获取对象类型信息
  instance_1 = instance;  // 保存参数字典指针
  v10 = *(unsigned __int16 *)(*instance + 302LL);  // 获取字段数量
  StringLiteral_15595 = StringLiteral_15595;  // 错误消息键名

  // 在参数字典中查找IDictionary类型的字段
  if ( *(_WORD *)(*instance + 302LL) )  // 如果有字段
  {
    v12 = (int *)(*(_QWORD *)(v8 + 176) + 8LL);  // 获取字段数组
    // 遍历字段，寻找IDictionary类型
    while ( *((_QWORD *)v12 - 1) != System_Collections_Generic_IDictionary_string__object__TypeInfo )
    {
      --v10;
      v12 += 4;  // 移动到下一个字段
      if ( !v10 )
        goto LABEL_20;  // 未找到，使用备用方法
    }
    v13 = v8 + 16LL * *v12 + 312;  // 计算字段地址
  }
  else
  {
LABEL_20:
    // 备用方法：通过类型查找获取字段
    v13 = sub_185BD18(instance, System_Collections_Generic_IDictionary_string__object__TypeInfo, 0LL);
  }

  // 从字典中获取错误消息
  instance = (_QWORD *)(*(__int64 (__fastcall **)(_QWORD *, __int64, _QWORD))v13)(
                         instance_1,
                         StringLiteral_15595,  // 错误消息键名
                         *(_QWORD *)(v13 + 8));

  if ( !v7 )  // 检查UI消息组件是否有效
    goto LABEL_33;

  // 检查返回的错误消息类型是否正确
  if ( instance && (_UNKNOWN *)*instance != string_TypeInfo )
  {
    // 类型不匹配，获取错误信息并调用错误处理
    v17 = sub_184E9B4(instance);
    return SFSController___cctor(v17);
  }

  // 显示具体的错误消息
  UI_Message__Show(v7, instance, 1LL, 0LL);

  // === 执行连接清理工作 ===
  // 断开SmartFox连接
  v14 = *(_QWORD *)(a1 + 64);  // 获取SmartFox实例
  if ( v14 )
    Sfs2X_SmartFox__Disconnect(v14, 0LL);  // 断开连接

  // 调用连接数据清理函数
  SFSController__DNIBMLBAGDM(a1, 1LL);

  // === 设置网络错误状态 ===
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = (_QWORD *)GameController__get_instance(0LL);
  if ( !instance || (instance = (_QWORD *)instance[15]) == 0LL )  // 获取网络管理器
LABEL_33:
    sub_184E634(instance);  // 错误处理/清理函数

  // 设置SFS连接错误标志
  result = NetworkManager__set_ErrorConnectingSFS(instance, 1LL, 0LL);

  // === 执行错误回调函数（如果存在）===
  v16 = *(_QWORD *)(a1 + 104);  // 获取错误回调函数指针
  if ( v16 )
    return (*(__int64 (__fastcall **)(_QWORD, _QWORD, _QWORD))(v16 + 24))(
             *(_QWORD *)(v16 + 64),  // 回调对象实例
             0LL,  // 错误参数（NULL表示连接失败）
             *(_QWORD *)(v16 + 40));  // 回调上下文数据

  return result;
}