# 反作弊标志位详细分析

## 概述
在 `SFSController__HCAJEPAGHLL` 函数中发现了多个反作弊检查点，这些检查点用于验证客户端的合法性并防止作弊行为。

## 主要反作弊检查函数

### 1. GameController__get_FukNut (第217行)
```c
FukNut = GameController__get_FukNut(deviceUniqueIdentifier, 0LL);
if ( (FukNut & 1) != 0 )
  goto LABEL_26; // 标记为作弊用户
```
- **作用**: 检查用户是否被标记为"FukNut"（可能是某种作弊标识）
- **返回值**: 布尔值，true表示检测到作弊
- **突破方法**: Hook此函数始终返回false (0)

### 2. GameController__Hacker (第227行和第476行)
```c
if ( (GameController__Hacker(deviceUniqueIdentifier, 0LL) & 1) != 0 )
  v14 = &StringLiteral_2043; // 标记为作弊用户
else
  v14 = &StringLiteral_1970; // 正常用户
```
- **作用**: 检查用户是否被识别为黑客
- **返回值**: 布尔值，true表示检测到黑客行为
- **突破方法**: Hook此函数始终返回false (0)
- **注意**: 此函数被调用了两次，需要都进行处理

### 3. GameController__get_GeneratedH (第361行)
```c
GeneratedH = GameController__get_GeneratedH(deviceUniqueIdentifier, 0LL);
Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_16275, GeneratedH & 1, 0LL);
```
- **作用**: 获取生成的哈希值状态
- **返回值**: 布尔值，可能与数据完整性检查相关
- **突破方法**: Hook此函数返回期望的值（通常是false）

### 4. GameController__get_UFO (第482行)
```c
UFO = GameController__get_UFO(0LL);
Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_19340, UFO & 1, 0LL);
```
- **作用**: 检查UFO标志（可能是某种异常检测）
- **返回值**: 布尔值
- **突破方法**: Hook此函数返回false (0)

### 5. GameController__get_TradeBanWord (第501行)
```c
TradeBanWord = GameController__get_TradeBanWord(deviceUniqueIdentifier, 0LL);
Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_19342, TradeBanWord & 1, 0LL);
```
- **作用**: 检查交易封禁词标志
- **返回值**: 布尔值，true表示触发了交易封禁
- **突破方法**: Hook此函数返回false (0)

### 6. GameController__HowOldAreYou (第241行)
```c
v67 = GameController__HowOldAreYou(deviceUniqueIdentifier, 0LL);
StringLiteral_1 = System_Double__ToString(&v67, 0LL);
```
- **作用**: 获取游戏时长或账户年龄
- **返回值**: double类型，表示时间
- **突破方法**: Hook此函数返回合理的时间值

## CodeStage AntiCheat ObscuredTypes 检查

### 7. ObscuredBool 检查点
多个位置使用了混淆布尔值：

#### a) 第271行 - SFS控制器布尔值
```c
v20 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
        *(_QWORD *)(a1 + 752),
        *(unsigned int *)(a1 + 760),
        0LL);
```
- **内存位置**: a1 + 752, a1 + 760
- **突破方法**: 直接修改内存值或Hook解密函数

#### b) 第307行 - 游戏控制器布尔值
```c
v26 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
        *(_QWORD *)(GameController_TypeInfo[23] + 124LL),
        *(unsigned int *)(GameController_TypeInfo[23] + 132LL),
        0LL);
```
- **内存位置**: GameController静态实例 + 124, + 132
- **突破方法**: 修改GameController实例中的混淆值

#### c) 第370行 - 另一个SFS控制器布尔值
```c
v33 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
        *(_QWORD *)(a1 + 692),
        *(unsigned int *)(a1 + 700),
        0LL);
```
- **内存位置**: a1 + 692, a1 + 700

#### d) 第452行 - 游戏控制器实例布尔值
```c
v46 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
        deviceUniqueIdentifier[52],
        *((unsigned int *)deviceUniqueIdentifier + 106),
        0LL);
```
- **内存位置**: GameController实例 + 52*8, + 106*4

#### e) 第488行 - 静态游戏控制器布尔值
```c
v50 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
        *(_QWORD *)(*((_QWORD *)GameController_TypeInfo + 23) + 40LL),
        *(unsigned int *)(*((_QWORD *)GameController_TypeInfo + 23) + 48LL),
        0LL);
```

### 8. ObscuredString 检查点

#### a) 第297行 - 网络管理器字符串
```c
v24 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
        *(_QWORD *)(NetworkManager_TypeInfo[23] + 24LL),
        0LL);
```

#### b) 第375-385行 - 多个字符串值
```c
v34 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
        *(_QWORD *)(a1 + 704), 0LL);
v35 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
        *(_QWORD *)(a1 + 712), 0LL);
v36 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
        *(_QWORD *)(a1 + 720), 0LL);
```

#### c) 第542行 - 游戏控制器字符串
```c
v53 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
        deviceUniqueIdentifier[56], 0LL);
```

#### d) 第655行 - 用户名字符串
```c
v63 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
        deviceUniqueIdentifier[35], 0LL);
```

### 9. 其他ObscuredTypes

#### ObscuredInt (第397行)
```c
v37 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
        *(_QWORD *)(a1 + 276),
        *(_QWORD *)(a1 + 284));
```

#### ObscuredShort (第445行)
```c
v45 = CodeStage_AntiCheat_ObscuredTypes_ObscuredShort__op_Implicit_26304492(
        *(_QWORD *)((char *)deviceUniqueIdentifier + 116),
        0LL);
```

#### ObscuredByte (第524行)
```c
v52 = CodeStage_AntiCheat_ObscuredTypes_ObscuredByte__op_Implicit_26302544(
        *(unsigned int *)(*(_QWORD *)deviceUniqueIdentifier[23] + 392LL) | 
        ((unsigned __int64)*(unsigned __int8 *)(*(_QWORD *)deviceUniqueIdentifier[23] + 396LL) << 32),
        0LL);
```

### 10. ObscuredPrefs 检查 (第550-556行)
```c
object = CodeStage_AntiCheat_Storage_ObscuredPrefs__Get_object_(
           StringLiteral_19371,
           StringLiteral_1,
           Method_CodeStage_AntiCheat_Storage_ObscuredPrefs_Get_string___);
```
- **作用**: 从加密的偏好设置中获取数据
- **突破方法**: Hook ObscuredPrefs的Get方法

## 突破策略总结

### 1. 函数Hook方法
- Hook所有GameController的反作弊检查函数，让它们返回"安全"值
- Hook ObscuredTypes的解密函数，返回预期值

### 2. 内存修改方法
- 直接修改SFSController实例中的混淆值
- 修改GameController静态实例中的反作弊标志
- 修改各种ObscuredTypes的加密数据

### 3. 关键内存偏移
- SFSController: a1 + 692, a1 + 700, a1 + 704, a1 + 712, a1 + 720, a1 + 752, a1 + 760
- GameController实例: +35*8(用户名), +52*8(布尔值), +56*8(字符串)
- GameController静态: +40, +48, +124, +132

### 4. 建议的Hook优先级
1. **高优先级**: GameController__get_FukNut, GameController__Hacker
2. **中优先级**: GameController__get_UFO, GameController__get_TradeBanWord
3. **低优先级**: ObscuredTypes解密函数（可以通过内存修改替代）

通过系统性地处理这些反作弊检查点，可以有效绕过游戏的反作弊系统。
