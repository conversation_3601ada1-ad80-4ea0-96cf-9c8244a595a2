__int64 __fastcall CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__InternalDecrypt(__int64 a1)
{
  unsigned int v2; // w19
  __int64 v3; // x0
  __int64 v4; // x0
  __int64 v5; // x0
  unsigned int IntKey; // w0

  if ( (byte_490B7A0 & 1) == 0 )
  {
    sub_184E408(&Method_CodeStage_AntiCheat_Common_KeepAliveBehaviour_ObscuredCheatingDetector__get_Instance__);
    byte_490B7A0 = 1;
  }
  if ( *(_BYTE *)(a1 + 13) )
  {
    v2 = *(_DWORD *)a1 ^ *(_DWORD *)(a1 + 4);
    if ( (CodeStage_AntiCheat_Detectors_ObscuredCheatingDetector__get_ExistsAndIsRunning(0LL) & 1) != 0
      && *(_BYTE *)(a1 + 12)
      && v2 != *(_DWORD *)(a1 + 8) )
    {
      v3 = *((_QWORD *)Method_CodeStage_AntiCheat_Common_KeepAliveBehaviour_ObscuredCheatingDetector__get_Instance__ + 4);
      if ( (*(_BYTE *)(v3 + 309) & 1) == 0 )
        v3 = sub_185BC14();
      v4 = *(_QWORD *)(*(_QWORD *)(v3 + 192) + 16LL);
      if ( (*(_BYTE *)(v4 + 309) & 1) == 0 )
        v4 = sub_185BC14();
      v5 = **(_QWORD **)(v4 + 184);
      if ( !v5 )
        sub_184E634(0LL);
      (*(void (__fastcall **)(__int64, _QWORD))(*(_QWORD *)v5 + 504LL))(v5, *(_QWORD *)(*(_QWORD *)v5 + 512LL));
    }
  }
  else
  {
    IntKey = CodeStage_AntiCheat_Utils_RandomUtils__GenerateIntKey();
    v2 = 0;
    *(_DWORD *)a1 = IntKey;
    *(_QWORD *)(a1 + 4) = IntKey;
    *(_WORD *)(a1 + 12) = 256;
  }
  return v2;
}