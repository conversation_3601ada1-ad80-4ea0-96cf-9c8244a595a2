	public TMPro.TextMesh<PERSON>roUGUI damageText; // 0x30
	public System.Void SendDamaged(System.Int32 NMKEBODLFOH, System.Int32 GILCJDDNJEG, System.Int32 JPEELDCHBGB, System.Int32 PKDCJDMHJBG, System.Single CEODMLCJCBL, PKALCBKGBCB GHBDPKDEKKL); // 0x199de44
	public System.Void PlayerTakeDamage(System.Byte JPEELDCHBGB, System.Int32 GILCJDDNJEG); // 0x19a5128
	public System.Void PlayerTakeDamage(System.Int16 LLMCGPAPBAC, System.Int16 GILCJDDNJEG, System.Byte HGBKGNPMLGM, System.Byte MFIKOANGMHG); // 0x19a7f1c
	public System.Void UnitTakeDamage(System.Int32 KJIGMPCEDAN, System.Int32 KDHGJINEAIB, System.Byte JPEELDCHBGB, System.Int32 GILCJDD<PERSON>J<PERSON>, System.Int32 LBKFNFHMIFB, System.Byte EPFOPHNBNBN); // 0x19a8af4
	public override System.Boolean TakeDamage(System.Int32 JGBAIPFFCLH, System.Single FMOCBAKLGJB); // 0x230b7d4
	public System.Boolean allowWeaponDamage; // 0x1d0
	public System.Int32 toolDamage; // 0x1e0
	public CodeStage.AntiCheat.ObscuredTypes.ObscuredInt _minDamage; // 0x28c
	public CodeStage.AntiCheat.ObscuredTypes.ObscuredInt _maxDamage; // 0x29c
	public System.Int32 get_Damage(); // 0x238a358
	public System.Void RPCNodeTakeDamage(System.Int32 LEOKPEDMDCN, System.Int32 JGBAIPFFCLH); // 0x237f6ec
	public System.Void RPCNodeTakeDamage(System.Int32 LEOKPEDMDCN, System.Int16 JGBAIPFFCLH); // 0x2381200
	private CodeStage.AntiCheat.ObscuredTypes.ObscuredInt _damage; // 0xa8
	public System.Int32 get_damage(); // 0x239b088
private sealed class DamageText.MEBJHAIMFBH : System.Object, System.Collections.Generic.IEnumerator<System.Single>, System.Collections.IEnumerator, System.IDisposable
	public DamageText <>4__this; // 0x18
public class DamageText : UnityEngine.MonoBehaviour
	public System.Void SetTrapDamage(System.Int32 GILCJDDNJEG, UnityEngine.Transform ADFCFBHCCOF, System.Boolean AEABDBPMHFK, UnityEngine.Vector3 PPLCIJHHJAM); // 0x2520304
	public System.Void SetDamage(System.Int32 GILCJDDNJEG, UnityEngine.Transform ADFCFBHCCOF, System.Boolean AEABDBPMHFK, UnityEngine.Vector3 PPLCIJHHJAM); // 0x2520940
	public static CodeStage.AntiCheat.ObscuredTypes.ObscuredInt damageTaken; // 0x88
	public virtual System.Void RPCTakeDamage(System.Int32 GILCJDDNJEG); // 0x283d92c
	public virtual System.Void RPCTakeDamage(System.Int16 GILCJDDNJEG); // 0x283eed0
	public const JOGDIFEALFP CriticalDamage = 2; // 0x0
	public override System.Boolean TakeDamage(System.Int32 JGBAIPFFCLH, System.Single FMOCBAKLGJB, System.Boolean DFOMHFEHGOF, System.Int32 PKDCJDMHJBG, System.Int32 KFCCAHCFLDC); // 0x1be3960
	protected System.Void RpcTakeDamage(PKALCBKGBCB GGJHAGAIGCC, System.Int16 JGBAIPFFCLH, System.Single FMOCBAKLGJB, System.Boolean DFOMHFEHGOF); // 0x1be6ec8
	public override System.Boolean TakeDamage(System.Int32 JGBAIPFFCLH, System.Single FMOCBAKLGJB); // 0x1be97cc
	protected System.Void RpcTakeDamage(System.Int32 JGBAIPFFCLH, System.Single FMOCBAKLGJB, System.Boolean DFOMHFEHGOF); // 0x1bec06c
	public System.Boolean OverrideDamage(System.Int32 JGBAIPFFCLH, System.Int32 LEOKPEDMDCN); // 0x1e42768
	public System.Boolean TakeDamage(System.Int32 JGBAIPFFCLH); // 0x1e451dc
	public override System.Void RPCTakeDamage(System.Int16 GILCJDDNJEG); // 0x1f30a38
	public override System.Void RPCTakeDamage(System.Int32 GILCJDDNJEG); // 0x1f3a444
	public virtual System.Boolean TakeDamage(System.Int32 JGBAIPFFCLH, System.Single FMOCBAKLGJB, System.Boolean DFOMHFEHGOF, System.Int32 PKDCJDMHJBG, System.Int32 KFCCAHCFLDC); // 0x20ca314
	public virtual System.Boolean TakeDamage(System.Int32 JGBAIPFFCLH, System.Single FMOCBAKLGJB); // 0x20cb10c
	public const CCEMFACHECL takeDamage = 9; // 0x0
	public const CCEMFACHECL woodTrapDamage = 39; // 0x0
	public const CodeStage.AntiCheat.Storage.ObscuredFileErrorCode FileDamaged = 10; // 0x0
