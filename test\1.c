__int64 __fastcall Enemy__HOHHLKELAFB(__int64 a1)
{
  _QWORD **SFSController_TypeInfo; // x0
  __int64 n4; // x0
  _QWORD **SFSController_TypeInfo_1; // x0
  _QWORD **SFSController_TypeInfo_2; // x0
  __int64 v6; // x0
  __int64 v7; // x1
  _QWORD **NetworkManager_TypeInfo; // x0
  __int64 v9; // x8
  __int64 *v10; // x27
  __int64 v11; // x21
  int id; // w0
  _QWORD **SFSController_TypeInfo_3; // x0
  __int64 v14; // x21
  __int64 n4_9; // x22
  __int64 v16; // x0
  __int64 v17; // x21
  long double position; // q0
  long double v19; // q1
  long double v20; // q2
  __int64 v21; // x8
  __int64 v22; // x22
  long double position_1; // q8
  long double v24; // q9
  long double v25; // q10
  unsigned int Quantity; // w23
  unsigned int v27; // w24
  unsigned int v28; // w25
  __int64 v29; // x8
  __int64 (__fastcall **v30)(); // x28
  _QWORD **SFSController_TypeInfo_4; // x0
  unsigned int n4_1; // w21
  int maxLoot; // w0
  __int64 n4_2; // x1
  _QWORD **SFSController_TypeInfo_5; // x0
  __int64 v36; // x21
  __int64 v37; // x0
  _QWORD **SFSController_TypeInfo_6; // x0
  __int64 v39; // x8
  __int64 n4_3; // x21
  __int64 v41; // x9
  int *v42; // x10
  __int64 v43; // x0
  __int64 v44; // x21
  __int64 *v45; // x8
  __int64 v46; // x22
  __int64 v47; // x8
  __int64 v48; // x9
  int *v49; // x10
  __int64 v50; // x0
  __int64 v51; // x8
  __int64 v52; // x9
  int *v53; // x10
  __int64 v54; // x0
  __int64 n4_4; // x23
  __int64 v56; // x10
  __int64 v57; // x8
  __int64 v58; // x24
  _QWORD **SFSController_TypeInfo_7; // x0
  __int64 v60; // x24
  unsigned int n323345; // w24
  __int64 n4_7; // x25
  char v63; // w25
  _QWORD *v64; // x8
  __int64 v65; // x26
  _QWORD *v66; // x8
  __int64 v67; // x24
  __int64 v68; // x24
  long double v69; // q8
  long double v70; // q1
  long double v71; // q9
  long double v72; // q2
  long double v73; // q10
  unsigned int v74; // w25
  __int64 v75; // x23
  unsigned int n4_8; // w26
  unsigned int v77; // w28
  __int64 v78; // x8
  __int64 v79; // x23
  unsigned int v80; // w25
  __int64 v81; // x23
  int v82; // w1
  int v83; // w24
  _QWORD *GameController_TypeInfo; // x0
  __int64 v85; // x8
  __int64 *v86; // x8
  unsigned int v87; // w23
  __int64 v88; // x23
  _QWORD *GameController_TypeInfo_1; // x0
  int v90; // w1
  int v91; // w24
  __int64 v92; // x8
  _QWORD **SFSController_TypeInfo_8; // x0
  __int64 v94; // x19
  __int64 n4_5; // x20
  __int64 v96; // x0
  __int64 n4_6; // x19
  __int64 v98; // x0
  __int64 v99; // x0
  __int64 v100; // x19
  __int64 v101; // x0
  int v102; // [xsp+8h] [xbp-B8h]
  unsigned int v103; // [xsp+20h] [xbp-A0h]
  unsigned int v104; // [xsp+24h] [xbp-9Ch]
  unsigned __int64 v105; // [xsp+28h] [xbp-98h]
  __int64 v106; // [xsp+38h] [xbp-88h] BYREF
  __int64 n9; // [xsp+48h] [xbp-78h] BYREF

  if ( (byte_490D09A & 1) == 0 )
  {
    sub_184E408(&UnityEngine_Application_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&System_Collections_Generic_IEnumerable_IEPDBNJKNLM__TypeInfo);
    sub_184E408(&System_Collections_Generic_IEnumerator_IEPDBNJKNLM__TypeInfo);
    sub_184E408(&System_Collections_IEnumerator_TypeInfo);
    sub_184E408(&MHLCNCDHBNN_TypeInfo);
    sub_184E408(&NetworkManager_TypeInfo);
    sub_184E408(&UnityEngine_Object_TypeInfo);
    sub_184E408(&SFSController_TypeInfo);
    sub_184E408(&StringLiteral_7865);
    sub_184E408(&StringLiteral_19624);
    sub_184E408(&StringLiteral_299);
    sub_184E408(&StringLiteral_8498);
    sub_184E408(&StringLiteral_19626);
    sub_184E408(&StringLiteral_17568);
    sub_184E408(&StringLiteral_3894);
    sub_184E408(&StringLiteral_3357);
    sub_184E408(&StringLiteral_1);
    sub_184E408(&StringLiteral_3749);
    sub_184E408(&StringLiteral_6372);
    byte_490D09A = 1;
  }
  n9 = 0LL;
  v106 = 0LL;
  if ( (unsigned int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                       *(_QWORD *)(a1 + 176),
                       *(_QWORD *)(a1 + 184),
                       0LL) )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    }
    n4 = *SFSController_TypeInfo[23];
    if ( !n4 )
      goto LABEL_236;
    SFSController__LogHack(n4, StringLiteral_8498, 0LL);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
    }
    n4 = *SFSController_TypeInfo_1[23];
    if ( !n4 )
      goto LABEL_236;
    n4 = SFSController__get_Connected(n4, 0LL);
    if ( (n4 & 1) == 0 )
      return n4;
  }
  if ( (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
              *(_QWORD *)(a1 + 248),
              *(_QWORD *)(a1 + 256),
              0LL) >= 4 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
    }
    n4 = *SFSController_TypeInfo_2[23];
    if ( !n4 )
      goto LABEL_236;
    n4 = SFSController__get_Connected(n4, 0LL);
    if ( (n4 & 1) == 0 )
      return n4;
  }
  v6 = UnityEngine_Random__Range_69543328(1LL, 99999LL, 0LL);
  *(_QWORD *)(a1 + 176) = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit(v6, 0LL);
  *(_QWORD *)(a1 + 184) = v7;
  NetworkManager_TypeInfo = (_QWORD **)NetworkManager_TypeInfo;
  if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
    NetworkManager_TypeInfo = (_QWORD **)NetworkManager_TypeInfo;
  }
  n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
         *NetworkManager_TypeInfo[23],
         *((unsigned int *)NetworkManager_TypeInfo[23] + 2),
         0LL);
  if ( (n4 & 1) == 0 )
  {
    n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
           *(_QWORD *)(a1 + 88),
           *(unsigned int *)(a1 + 96),
           0LL);
    if ( (n4 & 1) != 0 )
    {
      v10 = (__int64 *)(a1 + 64);
      v9 = *(_QWORD *)(a1 + 64);
      if ( !v9 )
        goto LABEL_236;
      v11 = *(_QWORD *)(v9 + 552);
      if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
      n4 = UnityEngine_Object__op_Inequality(v11, 0LL, 0LL);
      if ( (n4 & 1) != 0 )
      {
        if ( !*v10 )
          goto LABEL_236;
        n4 = *(_QWORD *)(*v10 + 552);
        if ( !n4 )
          goto LABEL_236;
        id = ItemAsset__get_id(n4, 0LL);
        HIDWORD(v106) = id;
        if ( id != 267903 && id != 323255 && id != 960288 )
        {
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !byte_490BC66 )
          {
            sub_184E408(&SFSController_TypeInfo);
            byte_490BC66 = 1;
          }
          SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
          }
          v14 = *SFSController_TypeInfo_3[23];
          n4 = System_Int32__ToString((char *)&v106 + 4, 0LL);
          if ( !*v10 )
            goto LABEL_236;
          n4_9 = n4;
          LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
          v16 = System_Int32__ToString(&v106, 0LL);
          n4 = System_String__Concat_60662184(StringLiteral_3357, n4_9, StringLiteral_299, v16, 0LL);
          if ( !v14 )
            goto LABEL_236;
          SFSController__SendWarn(v14, n4, StringLiteral_1, 0LL);
        }
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        n4 = GameController__get_instance(0LL);
        if ( !n4 )
          goto LABEL_236;
        if ( !*(_QWORD *)(a1 + 136) )
          goto LABEL_236;
        v17 = *(_QWORD *)(n4 + 104);
        position = UnityEngine_Transform__get_position(*(_QWORD *)(a1 + 136), 0LL);
        v21 = *v10;
        if ( !*v10 )
          goto LABEL_236;
        v22 = *(_QWORD *)(v21 + 552);
        if ( !v22 )
          goto LABEL_236;
        position_1 = position;
        v24 = v19;
        v25 = v20;
        Quantity = ItemAsset__get_Quantity(*(_QWORD *)(v21 + 552), 0LL);
        v27 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                *(_QWORD *)(a1 + 248),
                *(_QWORD *)(a1 + 256),
                0LL);
        if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                *(_QWORD *)(a1 + 428),
                *(unsigned int *)(a1 + 436),
                0LL) & 1) != 0 )
        {
          v28 = 1;
        }
        else
        {
          n4 = *v10;
          if ( !*v10 )
            goto LABEL_236;
          v28 = UnitAsset__get_localDropOnly(n4, 0LL) & 1;
        }
        n4 = *v10;
        if ( !*v10 || (n4 = UnitAsset__get_id(n4, 0LL), !v17) )
LABEL_236:
          sub_184E634(n4);
        n4 = DropManager__DropItem(v17, 0LL, 0LL, v22, Quantity, v27, v28, 0LL, position_1, v24, v25, n4, 0, 0LL);
      }
      if ( !*v10 )
        goto LABEL_236;
      v29 = *(_QWORD *)(*v10 + 544);
      if ( !v29 )
        goto LABEL_236;
      if ( !*(_QWORD *)(v29 + 24) )
        return n4;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      v30 = off_490B000;
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
      }
      n4 = *SFSController_TypeInfo_4[23];
      if ( !n4 )
        goto LABEL_236;
      if ( (SFSController__get_SFSDropOnlyUnit(n4, 0LL) & 1) != 0 )
      {
        n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
        if ( (_DWORD)n4 == 4 )
          return n4;
        n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
        if ( (_DWORD)n4 == 5 )
          return n4;
        n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
        if ( (int)n4 > 6 )
          return n4;
      }
      n4 = *v10;
      if ( !*v10 )
        goto LABEL_236;
      n4 = UnitAsset__get_minLoot(n4, 0LL);
      if ( !*v10 )
        goto LABEL_236;
      n4_1 = n4;
      maxLoot = UnitAsset__get_maxLoot(*v10, 0LL);
      n4 = UnityEngine_Random__Range_69543328(n4_1, (unsigned int)(maxLoot + 1), 0LL);
      HIDWORD(n9) = n4;
      if ( !*v10 )
        goto LABEL_236;
      n4_2 = (unsigned int)n4;
      n4 = *(_QWORD *)(*v10 + 576);
      if ( !n4 )
        goto LABEL_236;
      PJDGMFMMKID__CFEEKELBNMC(n4, n4_2, 0LL);
      if ( SHIDWORD(n9) >= 9 )
      {
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        if ( !byte_490BC66 )
        {
          sub_184E408(&SFSController_TypeInfo);
          byte_490BC66 = 1;
        }
        SFSController_TypeInfo_5 = (_QWORD **)SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_5 = (_QWORD **)SFSController_TypeInfo;
        }
        v36 = *SFSController_TypeInfo_5[23];
        v37 = System_Int32__ToString((char *)&n9 + 4, 0LL);
        n4 = System_String__Concat_60610504(StringLiteral_17568, v37, 0LL);
        if ( !v36 )
          goto LABEL_236;
        SFSController__LogHack(v36, n4, 0LL);
        if ( !byte_490BC66 )
        {
          sub_184E408(&SFSController_TypeInfo);
          byte_490BC66 = 1;
        }
        SFSController_TypeInfo_6 = (_QWORD **)SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_6 = (_QWORD **)SFSController_TypeInfo;
        }
        n4 = *SFSController_TypeInfo_6[23];
        if ( !n4 )
          goto LABEL_236;
        n4 = SFSController__get_Connected(n4, 0LL);
        if ( (n4 & 1) == 0 )
        {
          HIDWORD(n9) = 1;
          if ( !*v10 )
            goto LABEL_236;
          n4 = *(_QWORD *)(*v10 + 576);
          if ( !n4 )
            goto LABEL_236;
          PJDGMFMMKID__CFEEKELBNMC(n4, 1LL, 0LL);
        }
      }
      if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
      n4 = NetworkManager__get_IsMasterClient(0LL);
      if ( (n4 & 1) != 0 )
      {
        n4 = *v10;
        if ( !*v10 )
          goto LABEL_236;
        n4 = UnitAsset__get_Boss(n4, 0LL);
        if ( (n4 & 1) != 0 )
        {
          n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                 *(_QWORD *)(a1 + 428),
                 *(unsigned int *)(a1 + 436),
                 0LL);
          if ( (n4 & 1) == 0 )
          {
            n4 = *v10;
            if ( !*v10 )
              goto LABEL_236;
            n4 = UnitAsset__get_localDropOnly(n4, 0LL);
            if ( (n4 & 1) == 0 )
            {
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              if ( !byte_490BC66 )
              {
                sub_184E408(&SFSController_TypeInfo);
                byte_490BC66 = 1;
              }
              n4 = (__int64)SFSController_TypeInfo;
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              {
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                n4 = (__int64)SFSController_TypeInfo;
              }
              if ( *v10 )
              {
                v100 = **(_QWORD **)(n4 + 184);
                LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
                v101 = System_Int32__ToString(&v106, 0LL);
                n4 = System_String__Concat_60610504(StringLiteral_3894, v101, 0LL);
                if ( v100 )
                  return SFSController__SendWarn(v100, n4, StringLiteral_1, 0LL);
              }
              goto LABEL_236;
            }
          }
        }
      }
      LODWORD(n9) = 0;
      if ( !*v10 )
        goto LABEL_236;
      n4 = *(_QWORD *)(*v10 + 576);
      if ( !n4 )
        goto LABEL_236;
      n4 = (*(__int64 (__fastcall **)(__int64, _QWORD))(*(_QWORD *)n4 + 3608LL))(
             n4,
             *(_QWORD *)(*(_QWORD *)n4 + 3616LL));
      if ( !n4 )
        goto LABEL_236;
      v39 = *(_QWORD *)n4;
      n4_3 = n4;
      v41 = *(unsigned __int16 *)(*(_QWORD *)n4 + 302LL);
      if ( *(_WORD *)(*(_QWORD *)n4 + 302LL) )
      {
        v42 = (int *)(*(_QWORD *)(v39 + 176) + 8LL);
        while ( *((_QWORD *)v42 - 1) != System_Collections_Generic_IEnumerable_IEPDBNJKNLM__TypeInfo )
        {
          --v41;
          v42 += 4;
          if ( !v41 )
            goto LABEL_111;
        }
        v43 = v39 + 16LL * *v42 + 312;
      }
      else
      {
LABEL_111:
        v43 = sub_185BD18(n4, System_Collections_Generic_IEnumerable_IEPDBNJKNLM__TypeInfo, 0LL);
      }
      v44 = (*(__int64 (__fastcall **)(__int64, _QWORD))v43)(n4_3, *(_QWORD *)(v43 + 8));
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      n4 = (__int64)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        n4 = (__int64)SFSController_TypeInfo;
      }
      v45 = *(__int64 **)(n4 + 184);
      v46 = *v45;
      if ( !*v45 )
        goto LABEL_236;
      LODWORD(v106) = SFSController__get_MobsDrop(*v45, 0LL);
      n4 = SFSController__set_MobsDrop(v46, (int)v106 + 1);
      if ( !v44 )
        goto LABEL_236;
      while ( 1 )
      {
        while ( 1 )
        {
          while ( 1 )
          {
            v47 = *(_QWORD *)v44;
            v48 = *(unsigned __int16 *)(*(_QWORD *)v44 + 302LL);
            if ( *(_WORD *)(*(_QWORD *)v44 + 302LL) )
            {
              v49 = (int *)(*(_QWORD *)(v47 + 176) + 8LL);
              while ( *((_UNKNOWN **)v49 - 1) != System_Collections_IEnumerator_TypeInfo )
              {
                --v48;
                v49 += 4;
                if ( !v48 )
                  goto LABEL_125;
              }
              v50 = v47 + 16LL * *v49 + 312;
            }
            else
            {
LABEL_125:
              v50 = sub_185BD18(v44, System_Collections_IEnumerator_TypeInfo, 0LL);
            }
            n4 = (*(__int64 (__fastcall **)(__int64, _QWORD))v50)(v44, *(_QWORD *)(v50 + 8));
            if ( (n4 & 1) == 0 )
              return n4;
            v51 = *(_QWORD *)v44;
            v52 = *(unsigned __int16 *)(*(_QWORD *)v44 + 302LL);
            if ( *(_WORD *)(*(_QWORD *)v44 + 302LL) )
            {
              v53 = (int *)(*(_QWORD *)(v51 + 176) + 8LL);
              while ( *((_QWORD *)v53 - 1) != System_Collections_Generic_IEnumerator_IEPDBNJKNLM__TypeInfo )
              {
                --v52;
                v53 += 4;
                if ( !v52 )
                  goto LABEL_132;
              }
              v54 = v51 + 16LL * *v53 + 312;
            }
            else
            {
LABEL_132:
              v54 = sub_185BD18(v44, System_Collections_Generic_IEnumerator_IEPDBNJKNLM__TypeInfo, 0LL);
            }
            n4 = (*(__int64 (__fastcall **)(__int64, _QWORD))v54)(v44, *(_QWORD *)(v54 + 8));
            if ( !n4 )
              goto LABEL_236;
            n4_4 = n4;
            v56 = *((unsigned __int8 *)MHLCNCDHBNN_TypeInfo + 304);
            if ( *(unsigned __int8 *)(*(_QWORD *)n4 + 304LL) < (unsigned int)v56 )
              goto LABEL_236;
            if ( *(_UNKNOWN **)(*(_QWORD *)(*(_QWORD *)n4 + 200LL) + 8 * v56 - 8) != MHLCNCDHBNN_TypeInfo )
              goto LABEL_236;
            v57 = *(_QWORD *)(n4 + 104);
            if ( !v57 )
              goto LABEL_236;
            v58 = *(_QWORD *)(v57 + 136);
            if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
            if ( (UnityEngine_Object__op_Inequality(v58, 0LL, 0LL) & 1) != 0 )
            {
              n4 = *(_QWORD *)(n4_4 + 104);
              if ( !n4 )
                goto LABEL_236;
              if ( (ItemAsset__get_ExcludeDrop(n4, 0LL) & 1) == 0 )
                goto LABEL_239;
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              if ( !*((_BYTE *)v30 + 3174) )
              {
                sub_184E408(&SFSController_TypeInfo);
                *((_BYTE *)v30 + 3174) = 1;
              }
              SFSController_TypeInfo_7 = (_QWORD **)SFSController_TypeInfo;
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              {
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                SFSController_TypeInfo_7 = (_QWORD **)SFSController_TypeInfo;
              }
              n4 = *SFSController_TypeInfo_7[23];
              if ( !n4 )
                goto LABEL_236;
              if ( (SFSController__get_Connected(n4, 0LL) & 1) != 0 )
              {
LABEL_239:
                if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                  j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                if ( !*((_BYTE *)v30 + 3174) )
                {
                  sub_184E408(&SFSController_TypeInfo);
                  *((_BYTE *)v30 + 3174) = 1;
                }
                n4 = (__int64)SFSController_TypeInfo;
                if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                {
                  j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                  n4 = (__int64)SFSController_TypeInfo;
                }
                if ( !*(_QWORD *)(n4_4 + 104) )
                  goto LABEL_236;
                v60 = **(_QWORD **)(n4 + 184);
                n4 = ItemAsset__get_id(*(_QWORD *)(n4_4 + 104), 0LL);
                if ( !v60 )
                  goto LABEL_236;
                if ( (SFSController__DropExcluded(v60, (unsigned int)n4, 0LL) & 1) == 0 )
                  break;
              }
            }
          }
          if ( (int)n9 >= 9 )
          {
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !*((_BYTE *)v30 + 3174) )
            {
              sub_184E408(&SFSController_TypeInfo);
              *((_BYTE *)v30 + 3174) = 1;
            }
            SFSController_TypeInfo_8 = (_QWORD **)SFSController_TypeInfo;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              SFSController_TypeInfo_8 = (_QWORD **)SFSController_TypeInfo;
            }
            v94 = *SFSController_TypeInfo_8[23];
            n4 = System_Int32__ToString(&n9, 0LL);
            if ( *v10 )
            {
              n4_5 = n4;
              LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
              v96 = System_Int32__ToString(&v106, 0LL);
              n4 = System_String__Concat_60662184(StringLiteral_19624, n4_5, StringLiteral_299, v96, 0LL);
              if ( v94 )
              {
                SFSController__LogHack(v94, n4, 0LL);
                n4 = System_Int32__ToString(&n9, 0LL);
                if ( *v10 )
                {
                  n4_6 = n4;
                  LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
                  v98 = System_Int32__ToString(&v106, 0LL);
                  v99 = System_String__Concat_60662184(StringLiteral_19624, n4_6, StringLiteral_299, v98, 0LL);
                  SaveController__ConfirmHack(v99, StringLiteral_1, 0LL);
                  if ( !*((_DWORD *)UnityEngine_Application_TypeInfo + 56) )
                    j_il2cpp_runtime_class_init_0(UnityEngine_Application_TypeInfo);
                  return UnityEngine_Application__Quit_69396248(0LL);
                }
              }
            }
            goto LABEL_236;
          }
          n4 = *(_QWORD *)(n4_4 + 104);
          if ( !n4 )
            goto LABEL_236;
          n323345 = ItemAsset__get_id(n4, 0LL);
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          n4 = GameController__get_instance(0LL);
          if ( !*v10 )
            goto LABEL_236;
          n4_7 = n4;
          n4 = UnitAsset__get_id(*v10, 0LL);
          if ( !n4_7 )
            goto LABEL_236;
          if ( (GameController__ValidDrop(n4_7, (unsigned int)n4, n323345, 0LL) & 1) != 0 )
            break;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !*((_BYTE *)v30 + 3174) )
          {
            sub_184E408(&SFSController_TypeInfo);
            *((_BYTE *)v30 + 3174) = 1;
          }
          n4 = (__int64)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            n4 = (__int64)SFSController_TypeInfo;
          }
          if ( !*v10 )
            goto LABEL_236;
          v79 = **(_QWORD **)(n4 + 184);
          v80 = UnitAsset__get_id(*v10, 0LL);
          n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                 *(_QWORD *)(a1 + 248),
                 *(_QWORD *)(a1 + 256),
                 0LL);
          if ( !v79 )
            goto LABEL_236;
          SFSController__InvalidDropBan(v79, n323345, v80, (unsigned int)n4, 0LL);
          SaveController__ConfirmHack(StringLiteral_6372, StringLiteral_1, 0LL);
          v81 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(1LL, 0LL);
          v83 = v82;
          GameController_TypeInfo = GameController_TypeInfo;
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            GameController_TypeInfo = GameController_TypeInfo;
          }
          v85 = GameController_TypeInfo[23];
          *(_QWORD *)(v85 + 12) = v81;
          *(_DWORD *)(v85 + 20) = v83;
          v86 = &StringLiteral_7865;
LABEL_209:
          UnityEngine_MonoBehaviour__Invoke(a1, *v86, 0LL, 3.0);
        }
        n4 = *v10;
        if ( !*v10 )
          goto LABEL_236;
        v63 = UnitAsset__LBINHPGOKNK(n4, n323345, 0LL);
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        if ( !*((_BYTE *)v30 + 3174) )
        {
          sub_184E408(&SFSController_TypeInfo);
          *((_BYTE *)v30 + 3174) = 1;
        }
        n4 = (__int64)SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          n4 = (__int64)SFSController_TypeInfo;
        }
        v64 = *(_QWORD **)(n4 + 184);
        v65 = *v64;
        if ( (v63 & 1) == 0 )
        {
          n4 = *v10;
          if ( !*v10 )
            goto LABEL_236;
          v87 = UnitAsset__get_id(n4, 0LL);
          n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                 *(_QWORD *)(a1 + 248),
                 *(_QWORD *)(a1 + 256),
                 0LL);
          if ( !v65 )
            goto LABEL_236;
          SFSController__InvalidDropBan(v65, n323345, v87, (unsigned int)n4, 0LL);
          SaveController__ConfirmHack(StringLiteral_19626, StringLiteral_1, 0LL);
          v88 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(1LL, 0LL);
          GameController_TypeInfo_1 = GameController_TypeInfo;
          v91 = v90;
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            GameController_TypeInfo_1 = GameController_TypeInfo;
          }
          v92 = GameController_TypeInfo_1[23];
          *(_QWORD *)(v92 + 12) = v88;
          *(_DWORD *)(v92 + 20) = v91;
          v86 = &StringLiteral_3749;
          goto LABEL_209;
        }
        if ( !v65 )
          goto LABEL_236;
        if ( (SFSController__get_Connected(*v64, 0LL) & 1) != 0 || n323345 != -25801 && n323345 != 323345 )
        {
          LODWORD(n9) = n9 + 1;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !*((_BYTE *)v30 + 3174) )
          {
            sub_184E408(&SFSController_TypeInfo);
            *((_BYTE *)v30 + 3174) = 1;
          }
          n4 = (__int64)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            n4 = (__int64)SFSController_TypeInfo;
          }
          v66 = *(_QWORD **)(n4 + 184);
          v67 = *v66;
          if ( !*v66 )
            goto LABEL_236;
          LODWORD(v106) = SFSController__get_ItemsDroppedCount(*v66, 0LL);
          SFSController__set_ItemsDroppedCount(v67, (unsigned int)(v106 + 1), 0LL);
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          n4 = GameController__get_instance(0LL);
          if ( !n4 )
            goto LABEL_236;
          if ( !*(_QWORD *)(a1 + 136) )
            goto LABEL_236;
          v68 = *(_QWORD *)(n4 + 104);
          v69 = UnityEngine_Transform__get_position(*(_QWORD *)(a1 + 136), 0LL);
          v71 = v70;
          v73 = v72;
          v74 = UnityEngine_Random__Range_69543328(4294967146LL, 150LL, 0LL);
          n4 = UnityEngine_Random__Range_69543328(200LL, 250LL, 0LL);
          v75 = *(_QWORD *)(n4_4 + 104);
          if ( !v75 )
            goto LABEL_236;
          n4_8 = n4;
          v77 = ItemAsset__get_Quantity(v75, 0LL);
          v104 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                   *(_QWORD *)(a1 + 248),
                   *(_QWORD *)(a1 + 256),
                   0LL);
          v105 = v105 & 0xFFFFFFFF00000000LL | *(unsigned int *)(a1 + 436);
          if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(*(_QWORD *)(a1 + 428), v105, 0LL) & 1) != 0 )
          {
            LODWORD(v78) = 1;
          }
          else
          {
            n4 = *v10;
            if ( !*v10 )
              goto LABEL_236;
            v78 = UnitAsset__get_localDropOnly(n4, 0LL) & 1;
          }
          n4 = *v10;
          v103 = v78;
          if ( !*v10 )
            goto LABEL_236;
          n4 = UnitAsset__get_id(n4, 0LL);
          if ( !v68 )
            goto LABEL_236;
          LOBYTE(v102) = 0;
          DropManager__DropItem(v68, v74, n4_8, v75, v77, v104, v103, 0LL, v69, v71, v73, n4, v102, 0LL);
          v30 = off_490B000;
        }
      }
    }
  }
  return n4;
}