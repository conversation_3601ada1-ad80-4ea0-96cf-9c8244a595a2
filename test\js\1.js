// ---------- 1. 确认脚本已加载 ----------
console.log("🚀 JSHook/Frida 脚本已加载并开始执行！");
console.log("📦 目标模块1:", Process.findModuleByName("libil2cpp.so"));
console.log("📦 目标模块2:", Process.findModuleByName("Assembly-CSharp.dll"));
console.log("📦 目标模块3:", Process.findModuleByName("ACTk.Runtime.dll"));

// ---------- 2. 基础地址 ----------
const lib = Process.findModuleByName("libil2cpp.so");
const lib1 = Process.findModuleByName("Assembly-CSharp.dll");
const lib2 = Process.findModuleByName("ACTk.Runtime.dll");
const SFSController__HCAJEPAGHLL     = lib1.base.add(0x1990ee8);
const ObscuredString__op_Implicit    = lib2.base.add(0x1916090);

// ---------- 3. Hook ----------
Interceptor.attach(SFSController__HCAJEPAGHLL, {
    onEnter: function (args) {
        console.log("✅ SFSController__HCAJEPAGHLL 被调用，开始解密 serverKey …");

        const thisPtr          = args[0];
        const obscuredPtr      = thisPtr.add(0x2C8).readPointer();

        if (!obscuredPtr.isNull()) {
            const opImplicit = new NativeFunction(ObscuredString__op_Implicit,
                                                  'pointer', ['pointer']);
            const plainStrPtr = opImplicit(obscuredPtr);

            if (plainStrPtr && !plainStrPtr.isNull()) {
                console.log("🎯 还原后的字符串:", plainStrPtr.readUtf8String());
            } else {
                console.log("❌ 还原失败");
            }
        } else {
            console.log("⚠️ obscuredStringPtr 为空");
        }
    }
});