/**
 * 玩家伤害计算核心函数 - 基于护甲值计算最终伤害
 * @param a1: PlayerController实例指针
 * @param a2: 原始伤害值
 * @return: 计算后的最终伤害值
 *
 * 🛡️ 护甲系统说明：
 * - 护甲值越高，减伤效果越强
 * - 使用分段计算，不同护甲区间有不同的减伤公式
 * - 包含随机因子，避免伤害过于固定
 */
__int64 __fastcall PlayerController__MIDGHMNDKGI(__int64 a1, int a2)
{
  __int64 instance; // x0 - 实例指针
  int n159; // w20 - 玩家护甲值
  __int64 v6; // x8 - 游戏控制器数据指针
  int n19; // w0 - 玩家等级或ID
  __int64 n2; // x0 - 护甲减伤值
  float v9; // s0 - 浮点计算临时变量
  unsigned int n2_3; // w8 - 护甲减伤值（溢出情况）
  unsigned int n2_4; // w9 - 护甲减伤值（正常情况）
  float v12; // s0 - 高护甲值计算
  int v13; // w8 - 高护甲值整数部分
  float v14; // s0 - 中等护甲值计算
  bool v15; // zf - 浮点溢出检查标志
  float v16; // s0 - 低护甲值计算
  bool v17; // zf - 浮点溢出检查标志
  unsigned int n2_2; // w8 - 护甲减伤值（正常）
  unsigned int n2_1; // w9 - 护甲减伤值（溢出）
  float v20; // s0 - 极低护甲值计算
  int n301_1; // w0 - 随机护甲减伤值
  int v22; // w9 - 最终伤害计算结果
  int n301; // w8 - 护甲减伤值（用于边界检查）
  __int64 result; // x0 - 函数返回值

  // 【初始化检查】确保GameController类型信息已加载
  if ( (byte_490E3EE & 1) == 0 )
  {
    sub_184E408(&GameController_TypeInfo);
    byte_490E3EE = 1;
  }

  // 【获取玩家数据】从PlayerController获取玩家实例
  instance = *(_QWORD *)(a1 + 344);
  if ( !instance )
    goto LABEL_55;

  // 【获取护甲值】这是护甲计算的核心数值
  n159 = AIPPAPNPBNB__ECILJOCIIFO(instance, 0LL);

  // 【获取游戏控制器】用于获取玩家等级信息
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance || (v6 = *(_QWORD *)(instance + 112)) == 0 )
LABEL_55:
    sub_184E634(instance);

  // 【获取玩家等级/ID】使用ObscuredInt防作弊保护
  n19 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
          *(_QWORD *)(v6 + 72),
          *(_QWORD *)(v6 + 80),
          0LL);

  // 【特殊条件检查】低护甲值且高等级玩家的特殊处理
  // 💡 减伤关键点1：这里可以修改条件或增加伤害
  if ( n159 <= 159 && n19 >= 19 )
  {
    a2 += 40;  // 🔥 增加40点伤害！这里可以改为减少伤害
    goto LABEL_11;
  }
  // 【高护甲值处理】护甲值160-172的分段计算
  if ( n159 < 173 )
  {
    // 🛡️ 护甲值171-172：最高减伤等级
    if ( n159 >= 171 )
    {
      v9 = (float)n159 * 0.3;  // 护甲值的30%作为基础减伤
      n2_3 = -2147483609;      // 溢出时的默认值
      n2_4 = (int)v9 + 39;     // 💡 减伤关键点2：+39的额外减伤
      goto LABEL_45;
    }
    // 🛡️ 护甲值169-170
    if ( n159 >= 169 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483611;
      n2_4 = (int)v9 + 37;     // 💡 减伤关键点3：+37的额外减伤
      goto LABEL_45;
    }
    // 🛡️ 护甲值167-168
    if ( n159 >= 167 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483613;
      n2_4 = (int)v9 + 35;     // 💡 减伤关键点4：+35的额外减伤
      goto LABEL_45;
    }
    // 🛡️ 护甲值165-166
    if ( n159 >= 165 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483614;
      n2_4 = (int)v9 + 34;     // 💡 减伤关键点5：+34的额外减伤
      goto LABEL_45;
    }
    // 🛡️ 护甲值163-164
    if ( n159 >= 163 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483618;
      n2_4 = (int)v9 + 30;     // 💡 减伤关键点6：+30的额外减伤
      goto LABEL_45;
    }
    // 🛡️ 护甲值161-162
    if ( n159 >= 161 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483619;
      n2_4 = (int)v9 + 29;     // 💡 减伤关键点7：+29的额外减伤
      goto LABEL_45;
    }
LABEL_11:
    // 🛡️ 护甲值156-160：高等级减伤
    if ( n159 >= 156 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483620;
      n2_4 = (int)v9 + 28;     // 💡 减伤关键点8：+28的额外减伤
    }
    // 🛡️ 护甲值151-155：中高等级减伤
    else if ( n159 >= 151 )
    {
      v9 = (float)n159 * 0.3;
      n2_3 = -2147483621;
      n2_4 = (int)v9 + 27;     // 💡 减伤关键点9：+27的额外减伤
    }
    else
    {
      // 🛡️ 护甲值101-150：中等减伤（25%系数）
      if ( n159 >= 101 )
      {
        v14 = (float)n159 * 0.25;  // 💡 减伤关键点10：25%系数，比30%低
        n2_3 = -2147483629;
        n2_4 = (int)v14 + 19;      // 💡 减伤关键点11：+19的额外减伤
        v15 = v14 == INFINITY;
        goto LABEL_46;
      }

      // 🛡️ 护甲值0-50：低等级减伤处理
      if ( n159 < 51 )
      {
        // 护甲值31-50：15%系数
        if ( n159 >= 31 )
        {
          v16 = (float)n159 * 0.15;  // 💡 减伤关键点12：15%系数
          v17 = v16 == INFINITY;
          n2_2 = (int)v16 + 5;       // 💡 减伤关键点13：+5的额外减伤
          n2_1 = -2147483643;
        }
        else
        {
          // 护甲值0-20：最低减伤
          if ( n159 < 21 )
          {
            if ( n159 < 11 )
              n2 = 4294967293LL;  // 💡 减伤关键点14：极低护甲，几乎无减伤
            else
              n2 = 2LL;           // 💡 减伤关键点15：低护甲，减伤2点
            goto LABEL_49;
          }
          // 护甲值21-30：10%系数
          v20 = (float)n159 * 0.1;   // 💡 减伤关键点16：10%系数
          v17 = v20 == INFINITY;
          n2_2 = (int)v20 + 3;       // 💡 减伤关键点17：+3的额外减伤
          n2_1 = -2147483645;
        }
        if ( v17 )
          n2 = n2_1;
        else
          n2 = n2_2;
        goto LABEL_49;
      }
      // 🛡️ 护甲值51-100：16%系数
      v9 = (float)n159 * 0.16;     // 💡 减伤关键点18：16%系数
      n2_3 = -2147483639;
      n2_4 = (int)v9 + 9;          // 💡 减伤关键点19：+9的额外减伤
    }
LABEL_45:
    // 【浮点溢出检查】确保计算结果有效
    v15 = v9 == INFINITY;
LABEL_46:
    if ( v15 )
      n2 = n2_3;  // 使用溢出时的默认值
    else
      n2 = n2_4;  // 使用正常计算结果
    goto LABEL_49;
  }

  // 🛡️ 【超高护甲值处理】护甲值>=173的特殊计算
  v12 = (float)n159 * 0.3;
  v13 = (int)v12;
  if ( v12 == INFINITY )
    v13 = 0x80000000;
  n2 = (unsigned int)(n159 + v13 - 131);  // 💡 减伤关键点20：超高护甲的特殊公式

LABEL_49:
  // 【随机化减伤】在护甲减伤值范围内随机选择
  n301_1 = UnityEngine_Random__Range_69543328(n2, (unsigned int)n159, 0LL);

  // 【最终伤害计算】原始伤害 - 护甲减伤值
  v22 = a2 - (n301_1 & ~(n301_1 >> 31));  // 💡 减伤关键点21：这里是最终伤害计算！
  n301 = n301_1;

  // 【最小伤害保证】确保至少造成1点伤害
  if ( v22 <= 1 )
    result = 1LL;     // 💡 减伤关键点22：最小伤害为1，可以改为0实现完全免疫
  else
    result = (unsigned int)v22;

  // 【异常值检测】护甲值异常时返回随机伤害
  if ( n159 < 0 || n301 >= 301 )
    return UnityEngine_Random__Range_69543328(1LL, 99LL, 0LL);  // 💡 减伤关键点23：异常时的随机伤害

  return result;  // 返回最终计算的伤害值
}