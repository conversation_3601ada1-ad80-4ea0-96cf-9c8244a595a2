/**
 * SFS控制器 - 构建玩家数据包函数
 *
 * 功能描述：
 * 这个函数负责构建一个包含玩家所有重要数据的SFS对象，用于向服务器发送玩家的完整状态信息。
 * 通常在连接建立后或需要同步玩家数据时调用。
 *
 * 数据包内容包括：
 * - 多种类型的背包数据（装备、道具、材料等）
 * - 仓库存储数据
 * - 账户基本信息
 * - 任务进度数据
 *
 * @return __int64 返回构建完成的SFS对象指针
 *
 * 数据结构说明：
 * - StringLiteral_14484: 背包类型0的键名（可能是装备背包）
 * - StringLiteral_14491: 背包类型1的键名（可能是道具背包）
 * - StringLiteral_14496: 背包类型2的键名（可能是材料背包）
 * - StringLiteral_14228: 仓库数据的键名
 * - StringLiteral_14037: 账户数据的键名
 * - StringLiteral_18444: 任务数据的键名
 */
__int64 SFSController__GAAPMEDFDFL()
{
  __int64 v0; // x20 - SFS对象指针
  __int64 v1; // x0 - SaveController实例指针
  __int64 InventoryRaw; // x0 - 背包原始数据（类型0）
  __int64 v3; // x0 - SaveController实例指针
  __int64 v4; // x0 - 背包原始数据（类型1）
  __int64 v5; // x0 - SaveController实例指针
  __int64 v6; // x0 - 背包原始数据（类型2）
  __int64 v7; // x0 - SaveController实例指针
  __int64 StorageRaw; // x0 - 仓库原始数据
  __int64 v9; // x0 - SaveController实例指针
  __int64 AccountRaw; // x0 - 账户原始数据
  __int64 v11; // x0 - SaveController实例指针
  __int64 Quests; // x0 - 任务数据

  // 静态初始化检查 - 确保所有需要的类型信息和字符串字面量只初始化一次
  if ( (byte_490BC57 & 1) == 0 )
  {
    sub_184E408(&Sfs2X_Entities_Data_SFSObject_TypeInfo);  // SFS对象类型信息
    sub_184E408(&StringLiteral_14484);  // 背包类型0键名
    sub_184E408(&StringLiteral_14228);  // 仓库键名
    sub_184E408(&StringLiteral_14496);  // 背包类型2键名
    sub_184E408(&StringLiteral_18444);  // 任务键名
    sub_184E408(&StringLiteral_14491);  // 背包类型1键名
    sub_184E408(&StringLiteral_14037);  // 账户键名
    byte_490BC57 = 1;  // 标记已初始化
  }

  // 创建新的SFS对象实例
  v0 = sub_184E624(Sfs2X_Entities_Data_SFSObject_TypeInfo);  // 分配内存
  Sfs2X_Entities_Data_SFSObject___ctor(v0, 0LL);  // 调用构造函数

  // === 添加背包数据（类型0）===
  // 确保SaveController类型信息已初始化
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }

  // 获取SaveController单例实例
  v1 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v1 )
    sub_184E634(0LL);  // 实例为空，抛出异常

  // 加载背包类型0的原始数据
  InventoryRaw = SaveController__LoadInventoryRaw(v1, 0LL, 0LL);
  if ( !v0 )
    sub_184E634(InventoryRaw);  // SFS对象为空，抛出异常

  // 将背包数据添加到SFS对象中
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14484, InventoryRaw, 0LL);

  // === 添加背包数据（类型1）===
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v3 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v3 )
    sub_184E634(0LL);

  // 加载背包类型1的原始数据
  v4 = SaveController__LoadInventoryRaw(v3, 1LL, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14491, v4, 0LL);

  // === 添加背包数据（类型2）===
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v5 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v5 )
    sub_184E634(0LL);

  // 加载背包类型2的原始数据
  v6 = SaveController__LoadInventoryRaw(v5, 2LL, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14496, v6, 0LL);

  // === 添加仓库数据 ===
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v7 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v7 )
    sub_184E634(0LL);

  // 加载仓库原始数据（整数数组格式）
  StorageRaw = SaveController__LoadStorageRaw(v7, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutIntArray(v0, StringLiteral_14228, StorageRaw, 0LL);

  // === 添加账户数据 ===
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v9 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v9 )
    sub_184E634(0LL);

  // 加载账户原始数据
  AccountRaw = SaveController__LoadAccountRaw(v9, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14037, AccountRaw, 0LL);

  // === 添加任务数据 ===
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v11 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v11 )
    sub_184E634(0LL);

  // 加载任务数据（UTF字符串格式）
  Quests = SaveController__LoadQuests(v11, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutUtfString(v0, StringLiteral_18444, Quests, 0LL);

  return v0;  // 返回构建完成的SFS对象
}