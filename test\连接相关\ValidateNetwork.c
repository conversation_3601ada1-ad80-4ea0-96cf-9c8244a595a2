/**
 * SFS控制器 - 网络验证函数
 *
 * 功能描述：
 * 这个函数负责验证网络连接的合法性和安全性，是游戏反作弊系统的重要组成部分。
 * 主要检查以下几个方面：
 * 1. 网络验证是否启用
 * 2. 调试模式状态检查
 * 3. 网络环境安全性验证
 * 4. 用户身份验证
 * 5. 发送警告信息（如果检测到异常）
 *
 * 验证流程：
 * - 首先检查是否启用了网络验证功能
 * - 然后检查调试模式和网络环境状态
 * - 验证用户身份信息的一致性
 * - 如果发现异常，发送相应的警告消息到服务器
 *
 * @param a1 SFS控制器实例指针，包含连接配置和状态信息
 * @return __int64 验证结果：1表示验证通过，0表示验证失败
 *
 * 安全检查项目：
 * - StringLiteral_44: 调试模式检测警告消息
 * - StringLiteral_43: 网络环境异常警告消息
 */
__int64 __fastcall SFSController__ValidateNetwork(__int64 a1)
{
  __int64 DCEBNDFOAMB_TypeInfo_2; // x0 - 验证结果/类型信息指针
  _QWORD *DCEBNDFOAMB_TypeInfo; // x0 - 网络验证类型信息指针
  _QWORD *DCEBNDFOAMB_TypeInfo_1; // x0 - 网络验证类型信息指针（副本）
  __int64 instance; // x0 - 实例指针（多用途）
  __int64 v6; // x20 - 用户ID字符串指针
  __int64 v7; // x0 - 网络验证实例指针
  __int64 *v8; // x8 - 警告消息字符串指针
  __int64 v9; // x20 - 警告消息内容
  __int64 v10; // x2 - 用户ID或相关数据

  // 静态初始化检查 - 确保所有需要的类型信息只初始化一次
  if ( (byte_490BC60 & 1) == 0 )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);  // 网络验证类型信息
    sub_184E408(&GameController_TypeInfo);  // 游戏控制器类型信息
    sub_184E408(&StringLiteral_44);  // 调试模式警告消息
    sub_184E408(&StringLiteral_43);  // 网络环境警告消息
    byte_490BC60 = 1;  // 标记已初始化
  }

  // === 第一步：检查网络验证是否启用 ===
  // 通过反作弊系统的布尔值检查验证功能是否开启
  DCEBNDFOAMB_TypeInfo_2 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                             *(_QWORD *)(a1 + 652),  // 反作弊布尔值数据
                             *(unsigned int *)(a1 + 660),  // 反作弊布尔值密钥
                             0LL);

  // 如果网络验证未启用，直接返回（验证通过）
  if ( (DCEBNDFOAMB_TypeInfo_2 & 1) == 0 )
    return DCEBNDFOAMB_TypeInfo_2;

  // === 第二步：初始化网络验证类型信息 ===
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();

  if ( !byte_490BC6B )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    byte_490BC6B = 1;
  }

  // === 第三步：检查调试模式状态 ===
  DCEBNDFOAMB_TypeInfo = DCEBNDFOAMB_TypeInfo;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
  {
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo = DCEBNDFOAMB_TypeInfo;
  }

  // 检查调试模式标志位（偏移97字节处）
  if ( *(_BYTE *)(DCEBNDFOAMB_TypeInfo[23] + 97LL) )  // 如果调试模式开启
  {
    // 检查是否允许在调试模式下显示警告
    if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
            *(_QWORD *)(a1 + 752),  // 显示警告标志
            *(unsigned int *)(a1 + 760),
            0LL) & 1) != 0 )
      goto LABEL_48;  // 跳转到发送调试模式警告
    DCEBNDFOAMB_TypeInfo = DCEBNDFOAMB_TypeInfo;
  }

  // === 第四步：重新检查网络验证状态 ===
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();

  if ( !byte_490BC6B )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    byte_490BC6B = 1;
  }

  DCEBNDFOAMB_TypeInfo_1 = DCEBNDFOAMB_TypeInfo;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
  {
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo_1 = DCEBNDFOAMB_TypeInfo;
  }

  // 检查网络验证状态标志
  if ( !*(_BYTE *)(DCEBNDFOAMB_TypeInfo_1[23] + 97LL) )  // 如果网络验证未激活
  {
    // 检查是否需要显示网络环境警告
    if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
            *(_QWORD *)(a1 + 752),
            *(unsigned int *)(a1 + 760),
            0LL) & 1) != 0 )
    {
      DCEBNDFOAMB_TypeInfo_1 = DCEBNDFOAMB_TypeInfo;
      goto LABEL_22;  // 跳转到网络环境检查
    }
LABEL_48:
    // 发送调试模式检测警告
    if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
      ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    v7 = DCEBNDFOAMB__DJIBLEGCIFN(0LL);  // 获取网络验证实例
    v8 = &StringLiteral_44;  // 调试模式警告消息
    goto LABEL_51;  // 跳转到发送警告
  }

LABEL_22:
  // === 第五步：深度网络环境检查 ===
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo_1 + 56) )
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();

  if ( !byte_490BC6B )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    byte_490BC6B = 1;
  }

  DCEBNDFOAMB_TypeInfo_2 = (__int64)DCEBNDFOAMB_TypeInfo;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
  {
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo_2 = (__int64)DCEBNDFOAMB_TypeInfo;
  }

  // 检查网络环境安全标志
  if ( !*(_BYTE *)(*(_QWORD *)(DCEBNDFOAMB_TypeInfo_2 + 184) + 97LL) )
  {
    // 执行网络环境安全检查
    if ( !*(_DWORD *)(DCEBNDFOAMB_TypeInfo_2 + 224) )
      ((void (*)(void))j_il2cpp_runtime_class_init_0)();

    DCEBNDFOAMB_TypeInfo_2 = DCEBNDFOAMB__HNMKFNACEOP(0LL);  // 网络安全检查函数
    if ( (DCEBNDFOAMB_TypeInfo_2 & 1) != 0 )  // 如果检测到网络异常
    {
      // === 第六步：用户身份验证 ===
      if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
        ((void (*)(void))j_il2cpp_runtime_class_init_0)();

      if ( DCEBNDFOAMB__DJIBLEGCIFN(0LL) )  // 如果网络验证实例存在
      {
        if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
          ((void (*)(void))j_il2cpp_runtime_class_init_0)();

        instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
        if ( !instance )
          goto LABEL_64;  // 实例获取失败

        // 检查用户ID是否存在
        if ( *(_QWORD *)(instance + 32) )  // 偏移32字节处存储用户ID
        {
          // 获取网络验证实例中的用户ID
          if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
            ((void (*)(void))j_il2cpp_runtime_class_init_0)();

          instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
          if ( !instance )
            goto LABEL_64;

          v6 = *(_QWORD *)(instance + 32);  // 网络验证中的用户ID

          // 获取游戏控制器中的用户ID进行比较
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);

          instance = GameController__get_instance(0LL);
          if ( !instance )
            goto LABEL_64;

          // 解密游戏控制器中的用户ID
          instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                       *(_QWORD *)(instance + 280),  // 加密的用户ID
                       0LL);

          if ( !v6 )
            goto LABEL_64;

          // 比较两个用户ID是否一致
          DCEBNDFOAMB_TypeInfo_2 = System_String__Equals_60658464(v6, instance, 0LL);
          if ( (DCEBNDFOAMB_TypeInfo_2 & 1) != 0 )
            return DCEBNDFOAMB_TypeInfo_2;  // 验证通过，返回成功
        }
      }

      // === 第七步：发送网络环境异常警告 ===
      if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);

      v7 = DCEBNDFOAMB__DJIBLEGCIFN(0LL);  // 获取网络验证实例
      v8 = &StringLiteral_43;  // 网络环境异常警告消息

LABEL_51:
      // === 发送警告消息到服务器 ===
      v9 = *v8;  // 获取警告消息内容

      if ( v7 )  // 如果网络验证实例存在
      {
        if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);

        instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
        if ( instance )
        {
          v10 = *(_QWORD *)(instance + 32);  // 获取用户ID
          return SFSController__SendWarn(a1, v9, v10);  // 发送警告
        }
      }
      else  // 如果网络验证实例不存在，使用游戏控制器中的用户ID
      {
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);

        instance = GameController__get_instance(0LL);
        if ( instance )
        {
          // 解密用户ID
          v10 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(*(_QWORD *)(instance + 280), 0LL);
          return SFSController__SendWarn(a1, v9, v10);  // 发送警告
        }
      }

LABEL_64:
      sub_184E634(instance);  // 错误处理/清理函数
    }
  }

  return DCEBNDFOAMB_TypeInfo_2;  // 返回验证结果
}