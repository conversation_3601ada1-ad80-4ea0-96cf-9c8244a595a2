__int64 __fastcall SFSController__HLOJEKNFEFD(__int64 instance, __int64 a2)
{
  __int64 instance_1; // x19
  __int64 result; // x0
  __int64 v5; // x0
  int v6; // w1
  __int64 v7; // x20
  _QWORD *SFSController_TypeInfo; // x0
  __int64 v9; // x21
  __int64 v10; // x0
  _QWORD *v11; // x8
  _QWORD *SFSController_TypeInfo_1; // x0
  __int64 v13; // x21
  __int64 v14; // x21
  _QWORD *Sfs2X.Core.SFSEvent_TypeInfo; // x0
  __int64 v16; // x22
  __int64 v17; // x23
  __int64 v18; // x22
  __int64 v19; // x21
  __int64 v20; // x23
  __int64 v21; // x22
  __int64 v22; // x21
  __int64 v23; // x23
  __int64 v24; // x22
  __int64 v25; // x21
  __int64 v26; // x23
  __int64 v27; // x22
  __int64 v28; // x21
  __int64 v29; // x23

  instance_1 = instance;
  if ( (byte_490BC02 & 1) == 0 )
  {
    sub_184E408(&Sfs2X_Util_ConfigData_TypeInfo);
    sub_184E408(&Sfs2X_Core_EventListenerDelegate_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&Method_SFSController_DDCCAKIKPFH__);
    sub_184E408(&Method_SFSController_DKFHGPIMHAE__);
    sub_184E408(&Method_SFSController_KHCOGNCAGIG__);
    sub_184E408(&Method_SFSController_NJCIBNNPLCL__);
    sub_184E408(&Method_SFSController_PNJEOCPLAGP__);
    sub_184E408(&SFSController_TypeInfo);
    sub_184E408(&Sfs2X_Core_SFSEvent_TypeInfo);
    sub_184E408(&Sfs2X_SmartFox_TypeInfo);
    sub_184E408(&StringLiteral_9547);
    instance = sub_184E408(&StringLiteral_3018);
    byte_490BC02 = 1;
  }
  if ( !a2 )
    goto LABEL_34;
  result = System_String__Equals_60658464(a2, StringLiteral_9547, 0LL);
  if ( (result & 1) != 0 )
    return result;
  if ( *(_BYTE *)(instance_1 + 128) )
  {
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    instance = GameController__LCPDDKMGLFM(0LL);
    if ( instance )
    {
      instance = *(_QWORD *)(instance + 168);
      if ( instance )
        return ServerController__DACACJIFOOD(instance, a2, 0LL);
    }
LABEL_34:
    sub_184E634(instance);
  }
  v5 = *(_QWORD *)(instance_1 + 64);
  if ( !v5 )
    goto LABEL_15;
  result = Sfs2X_SmartFox__get_IsConnected(v5, 0LL);
  if ( (result & 1) == 0 )
  {
    instance = *(_QWORD *)(instance_1 + 64);
    if ( !instance )
      goto LABEL_34;
    result = Sfs2X_SmartFox__get_IsConnecting(instance, 0LL);
    if ( (result & 1) == 0 )
    {
LABEL_15:
      *(_QWORD *)(instance_1 + 768) = a2;
      *(_QWORD *)(instance_1 + 752) = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(1LL, 0LL);
      *(_DWORD *)(instance_1 + 760) = v6;
      *(_QWORD *)(instance_1 + 120) = 0LL;
      v7 = sub_184E624(Sfs2X_Util_ConfigData_TypeInfo);
      Sfs2X_Util_ConfigData___ctor(v7, 0LL);
      SFSController_TypeInfo = SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo = SFSController_TypeInfo;
      }
      v9 = *(_QWORD *)(SFSController_TypeInfo[23] + 8LL);
      v10 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit(StringLiteral_3018, 0LL);
      if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Equality(v9, v10, 0LL) & 1) != 0 )
      {
        v11 = (_QWORD *)(instance_1 + 72);
      }
      else
      {
        SFSController_TypeInfo_1 = SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_1 = SFSController_TypeInfo;
        }
        v11 = (_QWORD *)(SFSController_TypeInfo_1[23] + 8LL);
      }
      instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(*v11, 0LL);
      if ( !v7 )
        goto LABEL_34;
      *(_QWORD *)(v7 + 16) = instance;
      *(_DWORD *)(v7 + 24) = *(_DWORD *)(instance_1 + 80);
      *(_QWORD *)(v7 + 48) = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                               *(_QWORD *)(instance_1 + 88),
                               0LL);
      v13 = sub_184E624(Sfs2X_SmartFox_TypeInfo);
      instance = Sfs2X_SmartFox___ctor(v13, 0LL);
      *(_QWORD *)(instance_1 + 64) = v13;
      if ( !v13 )
        goto LABEL_34;
      Sfs2X_SmartFox__set_ThreadSafeMode(v13, 1LL, 0LL);
      instance = *(_QWORD *)(instance_1 + 64);
      if ( !instance )
        goto LABEL_34;
      Sfs2X_SmartFox__RemoveAllEventListeners(instance, 0LL);
      v14 = *(_QWORD *)(instance_1 + 64);
      Sfs2X.Core.SFSEvent_TypeInfo = Sfs2X_Core_SFSEvent_TypeInfo;
      if ( !*((_DWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(Sfs2X_Core_SFSEvent_TypeInfo);
        Sfs2X.Core.SFSEvent_TypeInfo = Sfs2X_Core_SFSEvent_TypeInfo;
      }
      v16 = *(_QWORD *)(Sfs2X.Core.SFSEvent_TypeInfo[23] + 16LL);
      v17 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
      instance = Sfs2X_Core_EventListenerDelegate___ctor(v17, instance_1, Method_SFSController_NJCIBNNPLCL__, 0LL);
      if ( !v14 )
        goto LABEL_34;
      Sfs2X_SmartFox__AddEventListener(v14, v16, v17, 0LL);
      v18 = *(_QWORD *)(instance_1 + 64);
      v19 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 40LL);
      v20 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
      instance = Sfs2X_Core_EventListenerDelegate___ctor(v20, instance_1, Method_SFSController_PNJEOCPLAGP__, 0LL);
      if ( !v18 )
        goto LABEL_34;
      Sfs2X_SmartFox__AddEventListener(v18, v19, v20, 0LL);
      v21 = *(_QWORD *)(instance_1 + 64);
      v22 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 88LL);
      v23 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
      instance = Sfs2X_Core_EventListenerDelegate___ctor(v23, instance_1, Method_SFSController_DKFHGPIMHAE__, 0LL);
      if ( !v21 )
        goto LABEL_34;
      Sfs2X_SmartFox__AddEventListener(v21, v22, v23, 0LL);
      v24 = *(_QWORD *)(instance_1 + 64);
      v25 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 96LL);
      v26 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
      instance = Sfs2X_Core_EventListenerDelegate___ctor(v26, instance_1, Method_SFSController_KHCOGNCAGIG__, 0LL);
      if ( !v24 )
        goto LABEL_34;
      Sfs2X_SmartFox__AddEventListener(v24, v25, v26, 0LL);
      v27 = *(_QWORD *)(instance_1 + 64);
      v28 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 408LL);
      v29 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
      instance = Sfs2X_Core_EventListenerDelegate___ctor(v29, instance_1, Method_SFSController_DDCCAKIKPFH__, 0LL);
      if ( !v27 )
        goto LABEL_34;
      Sfs2X_SmartFox__AddEventListener(v27, v28, v29, 0LL);
      instance = *(_QWORD *)(instance_1 + 64);
      if ( !instance )
        goto LABEL_34;
      return Sfs2X_SmartFox__Connect_63861312(instance, v7, 0LL);
    }
  }
  return result;
}