/**
 * 连接相关数据清理函数 - DNIBMLBAGDM
 *
 * 功能描述：
 * 这个函数负责清理和重置连接相关的数据结构，主要用于网络连接断开或重置时的资源清理。
 * 该函数是IL2CPP运行时的一部分，处理托管对象的清理工作。
 *
 * 主要操作：
 * 1. 初始化类型信息（如果尚未初始化）
 * 2. 检查并清理连接状态标志
 * 3. 清空连接相关的数组数据
 * 4. 重置连接计数器
 *
 * @return _QWORD* 返回类型信息指针
 */
_QWORD *FGHFGKNJOFD__DNIBMLBAGDM()
{
  _QWORD *FGHFGKNJOFD_TypeInfo; // x0 - 类型信息指针
  __int64 v1; // x8 - 静态数据指针
  unsigned __int64 v2; // x20 - 第一个循环计数器（清理连接对象数组）
  __int64 v3; // x8 - 连接对象数组指针
  unsigned __int64 i; // x20 - 第二个循环计数器（清理连接状态数组）
  __int64 v5; // x8 - 连接状态数组指针
  __int64 v6; // x8 - 连接状态数组指针（重新获取）
  __int64 v7; // x8 - 数组元素地址计算

  // 检查类型信息是否已初始化，如果没有则进行初始化
  if ( (byte_49104F5 & 1) == 0 )
  {
    sub_184E408(&FGHFGKNJOFD_TypeInfo);  // 初始化类型信息
    byte_49104F5 = 1;  // 设置初始化标志
  }

  FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;

  // 确保类型已完全初始化
  if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);  // IL2CPP运行时类初始化
    FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
  }

  // 获取静态数据区域
  v1 = FGHFGKNJOFD_TypeInfo[23];

  // 检查是否需要清理连接数据（通过标志位判断）
  if ( *(_BYTE *)(v1 + 80) )  // 连接清理标志位
  {
    // 确保类型已初始化
    if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
      FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      v1 = *((_QWORD *)FGHFGKNJOFD_TypeInfo + 23);
    }

    v2 = 0LL;  // 初始化循环计数器
    *(_BYTE *)(v1 + 80) = 0;  // 清除连接清理标志位

    // 第一个清理循环：清空连接对象数组
    while ( 1 )
    {
      // 再次确保类型已初始化
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      }

      // 获取连接对象数组指针
      v3 = *(_QWORD *)(FGHFGKNJOFD_TypeInfo[23] + 16LL);
      if ( !v3 )  // 如果数组不存在，跳转到清理完成
        goto LABEL_27;

      // 检查是否已遍历完所有元素
      if ( (__int64)v2 >= *(int *)(v3 + 24) )  // v3+24 是数组长度
        break;

      // 再次确保类型已初始化
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        FGHFGKNJOFD_TypeInfo = (_QWORD *)j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        v3 = *(_QWORD *)(*((_QWORD *)FGHFGKNJOFD_TypeInfo + 23) + 16LL);
        if ( !v3 )
          goto LABEL_27;
      }

      // 边界检查，防止数组越界
      if ( v2 >= *(unsigned int *)(v3 + 24) )
LABEL_28:
        sub_184E63C();  // 抛出越界异常

      // 清空当前连接对象（设置为NULL）
      *(_QWORD *)(v3 + 8 * v2 + 32) = 0LL;  // v3+32是数组数据起始位置，8字节对齐
      FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      ++v2;  // 移动到下一个元素
    }

    // 第二个清理循环：清空连接状态数组
    for ( i = 0LL; ; ++i )
    {
      // 确保类型已初始化
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      }

      // 获取连接状态数组指针
      v5 = *(_QWORD *)(FGHFGKNJOFD_TypeInfo[23] + 24LL);
      if ( !v5 )  // 如果数组不存在，退出循环
        break;

      // 检查是否已遍历完所有元素
      if ( (__int64)i >= *(int *)(v5 + 24) )
        return FGHFGKNJOFD_TypeInfo;  // 清理完成，返回类型信息

      // 再次确保类型已初始化
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      }

      // 重新获取连接状态数组指针
      v6 = *(_QWORD *)(FGHFGKNJOFD_TypeInfo[23] + 24LL);
      if ( !v6 )
        break;

      // 边界检查
      if ( i >= *(unsigned int *)(v6 + 24) )
        goto LABEL_28;

      // 计算当前元素地址并清零
      v7 = v6 + 4 * i;  // 4字节对齐（int类型）
      *(_DWORD *)(v7 + 32) = 0;  // 清零连接状态
    }

LABEL_27:
    // 执行最终清理操作
    sub_184E634(FGHFGKNJOFD_TypeInfo);  // 可能是垃圾回收或资源释放
  }

  return FGHFGKNJOFD_TypeInfo;  // 返回类型信息指针
}