__int64 __fastcall Enemy__Die(__int64 a1)
{
  __int64 instance; // x0
  __int64 v3; // x20
  __int128 *v4; // x22
  __int64 v5; // x20
  __int64 *v6; // x26
  _QWORD **SFSController_TypeInfo; // x0
  __int64 v8; // x20
  char IsOfflineMode; // w0
  __int64 StringLiteral_12690; // x21
  __int64 instance_1; // x2
  int v12; // w21
  float v13; // s0
  float v14; // s1
  float v15; // s8
  float v16; // s9
  _QWORD *v17; // x27
  long double position; // q0
  float v19; // s1
  float v20; // s10
  char v21; // w0
  char v22; // w0
  unsigned __int8 v23; // w23
  unsigned int instance_7; // w24
  long double v25; // q0
  long double v26; // q8
  long double v27; // q1
  long double v28; // q9
  unsigned int v29; // w21
  char IsMasterClient; // w20
  char IsMasterClient_1; // w25
  __int64 v32; // x21
  char v33; // w27
  unsigned int v34; // w0
  int v35; // w28
  int v36; // w29
  int v37; // w20
  unsigned int v38; // w26
  __int64 v39; // x8
  __int128 v40; // q0
  long double v41; // q0
  int n666; // w0
  __int64 *v43; // x22
  _QWORD **SFSController_TypeInfo_4; // x0
  __int64 v45; // x20
  __int64 instance_9; // x21
  __int64 v47; // x0
  unsigned int n2_3; // w8
  __int64 v49; // x0
  unsigned int n3_3; // w8
  __int64 v51; // x0
  __int64 v52; // x20
  __int64 v53; // x21
  __int64 v54; // x0
  __int128 v55; // q0
  __int128 v56; // q0
  int v57; // w1
  __int64 v58; // x20
  __int64 v59; // x20
  __int64 v60; // x8
  __int64 v61; // x20
  __int64 instance_10; // x20
  long double v63; // q0
  __int64 v64; // x20
  __int64 v65; // x20
  __int64 v66; // x21
  __int64 instance_2; // x20
  __int64 v69; // x0
  __int64 v70; // x0
  _QWORD **SFSController_TypeInfo_1; // x0
  __int64 v72; // x20
  __int64 instance_3; // x21
  __int64 v74; // x0
  unsigned int n2; // w8
  __int64 v76; // x0
  unsigned int n3; // w8
  __int64 v78; // x0
  __int64 v79; // x21
  __int64 instance_4; // x2
  __int64 v81; // x8
  __int64 v82; // x20
  __int64 id; // x0
  __int64 v84; // x1
  __int64 v85; // x20
  __int128 *v86; // x28
  unsigned int v87; // w21
  unsigned int v88; // w23
  __int64 v89; // x24
  int v90; // w20
  char v91; // w25
  char v92; // w26
  __int64 v93; // x0
  __int64 v94; // x0
  __int64 *v95; // x8
  int n199; // w0
  _QWORD **SFSController_TypeInfo_2; // x0
  __int64 v98; // x20
  __int64 instance_5; // x21
  __int64 v100; // x0
  unsigned int n2_1; // w8
  __int64 v102; // x0
  unsigned int n3_1; // w8
  __int64 v104; // x0
  unsigned int n5; // w8
  __int64 v106; // x0
  __int64 v107; // x8
  __int64 v108; // x0
  _QWORD **SFSController_TypeInfo_3; // x0
  __int64 v110; // x20
  __int64 instance_6; // x21
  __int64 v112; // x0
  unsigned int n2_2; // w8
  __int64 v114; // x0
  unsigned int n3_2; // w8
  __int64 v116; // x0
  unsigned int n5_1; // w8
  __int64 v118; // x0
  __int64 v119; // x20
  long double time; // q0
  float v121; // s8
  int v122; // w8
  long double v123; // q0
  float v124; // s9
  __int64 v125; // x20
  unsigned int v126; // w29
  unsigned int v127; // w22
  unsigned int v128; // w23
  __int64 v129; // x24
  unsigned int v130; // [xsp+2Ch] [xbp-164h]
  __int64 instance_8; // [xsp+30h] [xbp-160h]
  __int64 v132; // [xsp+30h] [xbp-160h]
  __int128 v133; // [xsp+38h] [xbp-158h] BYREF
  int v134; // [xsp+48h] [xbp-148h]
  __int128 v135; // [xsp+50h] [xbp-140h] BYREF
  int v136; // [xsp+60h] [xbp-130h]
  __int128 v137; // [xsp+70h] [xbp-120h]
  int v138; // [xsp+80h] [xbp-110h]
  __int128 v139; // [xsp+90h] [xbp-100h] BYREF
  int v140; // [xsp+A0h] [xbp-F0h]
  __int128 v141; // [xsp+B0h] [xbp-E0h] BYREF
  int v142; // [xsp+C0h] [xbp-D0h]
  __int128 v143; // [xsp+D0h] [xbp-C0h]
  int v144; // [xsp+E0h] [xbp-B0h]
  __int128 v145; // [xsp+F0h] [xbp-A0h] BYREF
  __int64 v146; // [xsp+108h] [xbp-88h] BYREF
  __int64 *v147; // [xsp+118h] [xbp-78h]

  if ( (byte_490D041 & 1) == 0 )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    sub_184E408(&Method_Enemy_HGMEHOONOCO__);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&Method_System_Collections_Generic_List_ObscuredInt__Contains__);
    sub_184E408(&NetworkManager_TypeInfo);
    sub_184E408(&UnityEngine_Object_TypeInfo);
    sub_184E408(&SFSController_TypeInfo);
    sub_184E408(&string___TypeInfo);
    sub_184E408(&DG_Tweening_TweenCallback_TypeInfo);
    sub_184E408(&Method_DG_Tweening_TweenSettingsExtensions_OnComplete_Tweener___);
    sub_184E408(&StringLiteral_7772);
    sub_184E408(&StringLiteral_10395);
    sub_184E408(&StringLiteral_253);
    sub_184E408(&StringLiteral_288);
    sub_184E408(&StringLiteral_299);
    sub_184E408(&StringLiteral_5711);
    sub_184E408(&StringLiteral_303);
    sub_184E408(&StringLiteral_413);
    sub_184E408(&StringLiteral_19627);
    sub_184E408(&StringLiteral_5710);
    sub_184E408(&StringLiteral_3745);
    sub_184E408(&StringLiteral_3892);
    sub_184E408(&StringLiteral_3746);
    sub_184E408(&StringLiteral_469);
    sub_184E408(&StringLiteral_300);
    sub_184E408(&StringLiteral_12690);
    sub_184E408(&StringLiteral_1);
    sub_184E408(&StringLiteral_3749);
    byte_490D041 = 1;
  }
  v146 = 0LL;
  v145 = 0uLL;
  v143 = 0uLL;
  v144 = 0;
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_300;
  v3 = *(_QWORD *)(instance + 112);
  v4 = (__int128 *)(a1 + 248);
  instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
  if ( !v3 )
    goto LABEL_300;
  instance = Map__Zone(v3, (unsigned int)instance, 0LL);
  if ( !instance )
    goto LABEL_300;
  Zone__RemoveEnemy(instance, a1, 0LL);
  instance = *(_QWORD *)(a1 + 160);
  if ( !instance )
    goto LABEL_300;
  UnityEngine_Behaviour__set_enabled(instance, 0LL, 0LL);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_300;
  if ( (GameController__get_Playing(instance, 0LL) & 1) == 0 )
  {
    v12 = 0;
    goto LABEL_111;
  }
  v6 = (__int64 *)(a1 + 64);
  v5 = *(_QWORD *)(a1 + 64);
  if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
  if ( (UnityEngine_Object__op_Equality(v5, 0LL, 0LL) & 1) != 0 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    }
    v8 = *SFSController_TypeInfo[23];
    if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
    IsOfflineMode = NetworkManager__get_IsOfflineMode(0LL);
    StringLiteral_12690 = StringLiteral_12690;
    if ( (IsOfflineMode & 1) != 0 )
    {
      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
      instance = GameController__get_instance(0LL);
      if ( !instance )
        goto LABEL_300;
      instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                   *(_QWORD *)(instance + 280),
                   0LL);
      instance_1 = instance;
    }
    else
    {
      if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
      instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
      if ( !instance )
        goto LABEL_300;
      instance_1 = *(_QWORD *)(instance + 32);
    }
    if ( !v8 )
      goto LABEL_300;
    SFSController__SendWarn(v8, StringLiteral_12690, instance_1);
  }
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_300;
  instance = *(_QWORD *)(instance + 80);
  if ( !instance )
    goto LABEL_300;
  PlayerController__get_Position(instance, 0LL);
  instance = *(_QWORD *)(a1 + 136);
  if ( !instance )
    goto LABEL_300;
  v15 = v13;
  v16 = v14;
  v17 = (_QWORD *)(a1 + 428);
  position = UnityEngine_Transform__get_position(instance, 0LL);
  v20 = (float)((float)(v15 - *(float *)&position) * (float)(v15 - *(float *)&position))
      + (float)((float)(v16 - v19) * (float)(v16 - v19));
  v21 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
          *(_QWORD *)(a1 + 88),
          *(unsigned int *)(a1 + 96),
          0LL);
  v147 = (__int64 *)(a1 + 64);
  if ( (v21 & 1) != 0 )
  {
    if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(*v17, *(unsigned int *)(a1 + 436), 0LL) & 1) != 0 )
      goto LABEL_46;
    instance = *v6;
    if ( !*v6 )
      goto LABEL_300;
    if ( (UnitAsset__get_localDropOnly(instance, 0LL) & 1) != 0 )
      goto LABEL_46;
    if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
    if ( (NetworkManager__get_IsMasterClient(0LL) & 1) != 0 )
    {
LABEL_46:
      v22 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(*v17, *(unsigned int *)(a1 + 436), 0LL);
      if ( v20 <= 121.0 || (v22 & 1) == 0 )
      {
        instance = *v6;
        if ( !*v6 )
          goto LABEL_300;
        if ( (UnitAsset__get_Boss(instance, 0LL) & 1) != 0 )
        {
          instance = *v6;
          if ( !*v6 )
            goto LABEL_300;
          if ( UnitAsset__get_sightRange(instance, 0LL) < 15.0 )
          {
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            HIDWORD(v146) = UnitAsset__get_id(instance, 0LL);
            instance = System_Int32__ToString((char *)&v146 + 4, 0LL);
            if ( !*v6 )
              goto LABEL_300;
            instance_2 = instance;
            LODWORD(v146) = UnitAsset__get_sightRange(*v6, 0LL);
            v69 = System_Single__ToString(&v146, 0LL);
            v70 = System_String__Concat_60662184(StringLiteral_10395, instance_2, StringLiteral_288, v69, 0LL);
            goto LABEL_252;
          }
        }
        instance = *v6;
        if ( !*v6 )
          goto LABEL_300;
        instance = UnitAsset__get_Boss(instance, 0LL);
        if ( (instance & 1) != 0 && *(int *)(a1 + 488) <= 0 )
        {
          instance = *(_QWORD *)(a1 + 104);
          if ( !instance )
            goto LABEL_300;
          instance = PhotonView__get_isMine(instance, 0LL);
          if ( (instance & 1) != 0 )
          {
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !byte_490BC66 )
            {
              sub_184E408(&SFSController_TypeInfo);
              byte_490BC66 = 1;
            }
            SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
            }
            v72 = *SFSController_TypeInfo_1[23];
            instance = sub_184E480(string___TypeInfo, 6LL);
            if ( !instance )
              goto LABEL_300;
            instance_3 = instance;
            if ( !*(_DWORD *)(instance + 24) )
              goto LABEL_301;
            *(_QWORD *)(instance + 32) = StringLiteral_3892;
            v74 = System_Int32__ToString(a1 + 488, 0LL);
            n2 = *(_DWORD *)(instance_3 + 24);
            if ( n2 <= 1 )
              goto LABEL_301;
            *(_QWORD *)(instance_3 + 40) = v74;
            if ( n2 == 2 )
              goto LABEL_301;
            *(_QWORD *)(instance_3 + 48) = StringLiteral_303;
            v145 = *v4;
            v76 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            n3 = *(_DWORD *)(instance_3 + 24);
            if ( n3 <= 3 )
              goto LABEL_301;
            *(_QWORD *)(instance_3 + 56) = v76;
            if ( n3 == 4 )
              goto LABEL_301;
            *(_QWORD *)(instance_3 + 64) = StringLiteral_299;
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            HIDWORD(v146) = UnitAsset__get_id(instance, 0LL);
            v78 = System_Int32__ToString((char *)&v146 + 4, 0LL);
            if ( *(_DWORD *)(instance_3 + 24) <= 5u )
              goto LABEL_301;
            *(_QWORD *)(instance_3 + 72) = v78;
            v79 = System_String__Concat_60662448(instance_3, 0LL);
            if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
            if ( (NetworkManager__get_IsOfflineMode(0LL) & 1) != 0 )
            {
              if ( !*((_DWORD *)GameController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
              instance = GameController__get_instance(0LL);
              if ( !instance )
                goto LABEL_300;
              instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                           *(_QWORD *)(instance + 280),
                           0LL);
              instance_4 = instance;
            }
            else
            {
              if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
              instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
              if ( !instance )
                goto LABEL_300;
              instance_4 = *(_QWORD *)(instance + 32);
            }
            if ( !v72 )
              goto LABEL_300;
            instance = SFSController__SendWarn(v72, v79, instance_4);
          }
        }
        if ( !byte_490D0AC )
        {
          instance = sub_184E408(&ValController_TypeInfo);
          byte_490D0AC = 1;
        }
        v81 = **((_QWORD **)ValController_TypeInfo + 23);
        if ( !v81 )
          goto LABEL_300;
        instance = *v6;
        if ( !*v6 )
          goto LABEL_300;
        v82 = *(_QWORD *)(v81 + 96);
        id = UnitAsset__get_id(instance, 0LL);
        instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit(id, 0LL);
        if ( !v82 )
          goto LABEL_300;
        if ( (System_Collections_Generic_List_ObscuredInt___Contains(
                v82,
                instance,
                v84,
                Method_System_Collections_Generic_List_ObscuredInt__Contains__) & 1) != 0 )
        {
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !byte_490BC66 )
          {
            sub_184E408(&SFSController_TypeInfo);
            byte_490BC66 = 1;
          }
          instance = (__int64)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            instance = (__int64)SFSController_TypeInfo;
          }
          if ( !*v6 )
            goto LABEL_300;
          v85 = **(_QWORD **)(instance + 184);
          v86 = (__int128 *)(a1 + 120);
          v87 = UnitAsset__get_id(*v6, 0LL);
          v88 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                  *(_QWORD *)(a1 + 120),
                  *(_QWORD *)(a1 + 128),
                  0LL);
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          instance = GameController__get_instance(0LL);
          if ( !instance )
            goto LABEL_300;
          v132 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                   *(_QWORD *)(instance + 280),
                   0LL);
          if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
          if ( (NetworkManager__get_IsOfflineMode(0LL) & 1) != 0 )
          {
            if ( !*((_DWORD *)GameController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            instance = GameController__get_instance(0LL);
            if ( !instance )
              goto LABEL_300;
            v89 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                    *(_QWORD *)(instance + 280),
                    0LL);
          }
          else
          {
            if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
            instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
            if ( !instance )
              goto LABEL_300;
            v89 = *(_QWORD *)(instance + 32);
          }
          if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
          v91 = NetworkManager__get_IsOfflineMode(0LL);
          v92 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                  *(_QWORD *)(a1 + 440),
                  *(unsigned int *)(a1 + 448),
                  0LL);
          instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                       *(_QWORD *)(a1 + 452),
                       *(_QWORD *)(a1 + 460),
                       0LL);
          if ( !v85 )
            goto LABEL_300;
          SFSController__Killed_26889360(
            v85,
            v87,
            v88,
            v132,
            v89,
            v91 & 1,
            v92 & 1,
            (unsigned int)instance,
            *(_DWORD *)(a1 + 620),
            0LL);
          if ( (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                      *(_QWORD *)(a1 + 120),
                      *(_QWORD *)(a1 + 128),
                      0LL) < 51 )
          {
            v145 = *v86;
            v93 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            v94 = System_String__Concat_60610504(StringLiteral_3746, v93, 0LL);
            SaveController__ConfirmHack(v94, StringLiteral_1, 0LL);
            v95 = &StringLiteral_3749;
            return UnityEngine_MonoBehaviour__Invoke(a1, *v95, 0LL, 3.0);
          }
          n199 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                   *(_QWORD *)(a1 + 120),
                   *(_QWORD *)(a1 + 128),
                   0LL);
          v6 = v147;
          if ( n199 <= 199 )
          {
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !byte_490BC66 )
            {
              sub_184E408(&SFSController_TypeInfo);
              byte_490BC66 = 1;
            }
            SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
            }
            v98 = *SFSController_TypeInfo_2[23];
            instance = sub_184E480(string___TypeInfo, 8LL);
            if ( !instance )
              goto LABEL_300;
            instance_5 = instance;
            if ( !*(_DWORD *)(instance + 24) )
              goto LABEL_301;
            *(_QWORD *)(instance + 32) = StringLiteral_19627;
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            HIDWORD(v146) = UnitAsset__get_id(instance, 0LL);
            v100 = System_Int32__ToString((char *)&v146 + 4, 0LL);
            n2_1 = *(_DWORD *)(instance_5 + 24);
            if ( n2_1 <= 1 )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 40) = v100;
            if ( n2_1 == 2 )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 48) = StringLiteral_413;
            v145 = *v86;
            v102 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            n3_1 = *(_DWORD *)(instance_5 + 24);
            if ( n3_1 <= 3 )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 56) = v102;
            if ( n3_1 == 4 )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 64) = StringLiteral_303;
            v145 = *v4;
            v104 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            n5 = *(_DWORD *)(instance_5 + 24);
            if ( n5 <= 5 )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 72) = v104;
            if ( n5 == 6 )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 80) = StringLiteral_288;
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            LODWORD(v146) = UnitAsset__get_sightRange(instance, 0LL);
            v106 = System_Single__ToString(&v146, 0LL);
            if ( *(_DWORD *)(instance_5 + 24) <= 7u )
              goto LABEL_301;
            *(_QWORD *)(instance_5 + 88) = v106;
            instance = System_String__Concat_60662448(instance_5, 0LL);
            if ( !v98 )
              goto LABEL_300;
            SFSController__SendWarn(v98, instance, StringLiteral_1);
          }
          instance = *v6;
          if ( !*v6 )
            goto LABEL_300;
          if ( (UnitAsset__get_Boss(instance, 0LL) & 1) != 0 )
          {
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            if ( (UnitAsset__get_Boss(instance, 0LL) & 1) == 0 )
              goto LABEL_273;
            v90 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                    *(_QWORD *)(a1 + 452),
                    *(_QWORD *)(a1 + 460),
                    0LL);
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !byte_490BC66 )
            {
              sub_184E408(&SFSController_TypeInfo);
              byte_490BC66 = 1;
            }
            instance = (__int64)SFSController_TypeInfo;
            if ( *((_DWORD *)SFSController_TypeInfo + 56) )
              goto LABEL_248;
            goto LABEL_247;
          }
        }
        else
        {
          instance = *v6;
          if ( !*v6 )
            goto LABEL_300;
          if ( (UnitAsset__get_Boss(instance, 0LL) & 1) != 0
            && (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                      *(_QWORD *)(a1 + 120),
                      *(_QWORD *)(a1 + 128),
                      0LL) <= 199 )
          {
            v145 = *(_OWORD *)(a1 + 120);
            v108 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            v70 = System_String__Concat_60610504(StringLiteral_3745, v108, 0LL);
LABEL_252:
            SaveController__ConfirmHack(v70, StringLiteral_1, 0LL);
            v95 = &StringLiteral_7772;
            return UnityEngine_MonoBehaviour__Invoke(a1, *v95, 0LL, 3.0);
          }
          if ( (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                      *(_QWORD *)(a1 + 248),
                      *(_QWORD *)(a1 + 256),
                      0LL) >= 8
            && (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                      *(_QWORD *)(a1 + 120),
                      *(_QWORD *)(a1 + 128),
                      0LL) <= 99 )
          {
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !byte_490BC66 )
            {
              sub_184E408(&SFSController_TypeInfo);
              byte_490BC66 = 1;
            }
            SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
            }
            v110 = *SFSController_TypeInfo_3[23];
            instance = sub_184E480(string___TypeInfo, 8LL);
            if ( !instance )
              goto LABEL_300;
            instance_6 = instance;
            if ( !*(_DWORD *)(instance + 24) )
              goto LABEL_301;
            *(_QWORD *)(instance + 32) = StringLiteral_19627;
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            HIDWORD(v146) = UnitAsset__get_id(instance, 0LL);
            v112 = System_Int32__ToString((char *)&v146 + 4, 0LL);
            n2_2 = *(_DWORD *)(instance_6 + 24);
            if ( n2_2 <= 1 )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 40) = v112;
            if ( n2_2 == 2 )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 48) = StringLiteral_413;
            v145 = *(_OWORD *)(a1 + 120);
            v114 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            n3_2 = *(_DWORD *)(instance_6 + 24);
            if ( n3_2 <= 3 )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 56) = v114;
            if ( n3_2 == 4 )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 64) = StringLiteral_303;
            v145 = *v4;
            v116 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__ToString(&v145, 0LL);
            n5_1 = *(_DWORD *)(instance_6 + 24);
            if ( n5_1 <= 5 )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 72) = v116;
            if ( n5_1 == 6 )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 80) = StringLiteral_288;
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            LODWORD(v146) = UnitAsset__get_sightRange(instance, 0LL);
            v118 = System_Single__ToString(&v146, 0LL);
            if ( *(_DWORD *)(instance_6 + 24) <= 7u )
              goto LABEL_301;
            *(_QWORD *)(instance_6 + 88) = v118;
            instance = System_String__Concat_60662448(instance_6, 0LL);
            if ( !v110 )
              goto LABEL_300;
            SFSController__SendWarn(v110, instance, StringLiteral_1);
            goto LABEL_273;
          }
          instance = *v6;
          if ( !*v6 )
            goto LABEL_300;
          if ( (UnitAsset__get_Boss(instance, 0LL) & 1) != 0 )
          {
            instance = *v6;
            if ( !*v6 )
              goto LABEL_300;
            if ( (UnitAsset__get_Boss(instance, 0LL) & 1) == 0 )
              goto LABEL_273;
            v90 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                    *(_QWORD *)(a1 + 452),
                    *(_QWORD *)(a1 + 460),
                    0LL);
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !byte_490BC66 )
            {
              sub_184E408(&SFSController_TypeInfo);
              byte_490BC66 = 1;
            }
            instance = (__int64)SFSController_TypeInfo;
            if ( *((_DWORD *)SFSController_TypeInfo + 56) )
              goto LABEL_248;
LABEL_247:
            j_il2cpp_runtime_class_init_0(instance);
            instance = (__int64)SFSController_TypeInfo;
LABEL_248:
            v107 = **(_QWORD **)(instance + 184);
            if ( !v107 )
              goto LABEL_300;
            if ( v90 <= (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                               *(_QWORD *)(v107 + 308),
                               *(_QWORD *)(v107 + 316),
                               0LL) )
              goto LABEL_273;
          }
        }
        Enemy__HOHHLKELAFB(a1);
LABEL_273:
        v119 = *v6;
        if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
        if ( (UnityEngine_Object__op_Inequality(v119, 0LL, 0LL) & 1) != 0 )
        {
          instance = *v6;
          if ( !*v6 )
            goto LABEL_300;
          if ( (UnitAsset__get_Boss(instance, 0LL) & 1) != 0 )
          {
            time = UnityEngine_Time__get_time(0LL);
            v121 = *(float *)&time;
            v122 = *(_DWORD *)(a1 + 484);
            v141 = *(_OWORD *)(a1 + 468);
            v142 = v122;
            v123 = CodeStage_AntiCheat_ObscuredTypes_ObscuredFloat__op_Implicit_26303476(&v141, 0LL);
            v124 = *(float *)&v123;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !byte_490BC66 )
            {
              sub_184E408(&SFSController_TypeInfo);
              byte_490BC66 = 1;
            }
            instance = (__int64)SFSController_TypeInfo;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              instance = (__int64)SFSController_TypeInfo;
            }
            if ( !*v6 )
              goto LABEL_300;
            v125 = **(_QWORD **)(instance + 184);
            v126 = UnitAsset__get_id(*v6, 0LL);
            v127 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                     *(_QWORD *)(a1 + 120),
                     *(_QWORD *)(a1 + 128),
                     0LL);
            v128 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                     *(_QWORD *)(a1 + 248),
                     *(_QWORD *)(a1 + 256),
                     0LL);
            if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
            if ( (NetworkManager__get_IsOfflineMode(0LL) & 1) != 0 )
            {
              if ( !*((_DWORD *)GameController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
              instance = GameController__get_instance(0LL);
              if ( !instance )
                goto LABEL_300;
              v129 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                       *(_QWORD *)(instance + 280),
                       0LL);
            }
            else
            {
              if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
              instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
              if ( !instance )
                goto LABEL_300;
              v129 = *(_QWORD *)(instance + 32);
            }
            if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
            instance = NetworkManager__get_IsOfflineMode(0LL);
            if ( !v125 )
              goto LABEL_300;
            SFSController__BossAliveKilled(v125, v126, v127, v128, v129, instance & 1, 0LL, v121 - v124);
          }
        }
      }
    }
  }
  v23 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
          *(_QWORD *)(a1 + 88),
          *(unsigned int *)(a1 + 96),
          0LL);
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
  if ( !byte_490BC66 )
  {
    sub_184E408(&SFSController_TypeInfo);
    byte_490BC66 = 1;
  }
  instance = (__int64)SFSController_TypeInfo;
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    instance = (__int64)SFSController_TypeInfo;
  }
  if ( !*v6 )
    goto LABEL_300;
  instance_8 = **(_QWORD **)(instance + 184);
  instance = UnitAsset__get_id(*v6, 0LL);
  if ( !*(_QWORD *)(a1 + 136) )
    goto LABEL_300;
  instance_7 = instance;
  v25 = UnityEngine_Transform__get_position(*(_QWORD *)(a1 + 136), 0LL);
  instance = *(_QWORD *)(a1 + 136);
  if ( !instance )
    goto LABEL_300;
  v26 = v25;
  UnityEngine_Transform__get_position(instance, 0LL);
  v28 = v27;
  v29 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
          *(_QWORD *)(a1 + 248),
          *(_QWORD *)(a1 + 256),
          0LL);
  if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
  IsMasterClient = NetworkManager__get_IsMasterClient(0LL);
  v130 = v29;
  if ( (NetworkManager__get_IsOfflineMode(0LL) & 1) != 0 )
  {
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    instance = GameController__get_instance(0LL);
    if ( !instance )
      goto LABEL_300;
    IsMasterClient_1 = IsMasterClient;
    v32 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(*(_QWORD *)(instance + 280), 0LL);
  }
  else
  {
    if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
    instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
    if ( !instance )
      goto LABEL_300;
    v32 = *(_QWORD *)(instance + 32);
    IsMasterClient_1 = IsMasterClient;
  }
  v33 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
          *(_QWORD *)(a1 + 440),
          *(unsigned int *)(a1 + 448),
          0LL);
  v34 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
          *(_QWORD *)(a1 + 452),
          *(_QWORD *)(a1 + 460),
          0LL);
  v35 = *(_DWORD *)(a1 + 492);
  v36 = *(_DWORD *)(a1 + 620);
  v37 = *(_DWORD *)(a1 + 532);
  v38 = v34;
  if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
  if ( (NetworkManager__get_IsMasterClient(0LL) & 1) != 0 )
    v39 = a1 + 536;
  else
    v39 = a1 + 556;
  v40 = *(_OWORD *)v39;
  v144 = *(_DWORD *)(v39 + 16);
  v143 = v40;
  v139 = v40;
  v140 = v144;
  v41 = CodeStage_AntiCheat_ObscuredTypes_ObscuredFloat__op_Implicit_26303476(&v139, 0LL);
  instance = instance_8;
  if ( !instance_8 )
    goto LABEL_300;
  SFSController__Killed(
    instance_8,
    instance_7,
    v130,
    IsMasterClient_1 & 1,
    v32,
    v33 & 1,
    v38,
    v23 & (v20 <= 121.0),
    v26,
    v28,
    v41,
    v35,
    v36,
    v37,
    0LL);
  n666 = Enemy__MHIGCKCCOID(a1);
  v43 = v147;
  if ( n666 == 666 )
    goto LABEL_80;
  instance = Enemy__MHIGCKCCOID(a1);
  if ( !*(_QWORD *)(a1 + 64) )
    goto LABEL_300;
  if ( (_DWORD)instance == (unsigned int)UnitAsset__get_moveSpeed(*(_QWORD *)(a1 + 64), 0LL) )
  {
LABEL_80:
    instance = *v43;
    if ( !*v43 )
      goto LABEL_300;
    if ( (int)UnitAsset__get_moveSpeed(instance, 0LL) > 0 )
      goto LABEL_98;
    instance = *v43;
    if ( !*v43 )
      goto LABEL_300;
    if ( (int)UnitAsset__get_maxHp(instance, 0LL) < 200 )
      goto LABEL_98;
  }
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
  if ( !byte_490BC66 )
  {
    sub_184E408(&SFSController_TypeInfo);
    byte_490BC66 = 1;
  }
  SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
  }
  v45 = *SFSController_TypeInfo_4[23];
  instance = sub_184E480(string___TypeInfo, 6LL);
  if ( !instance )
    goto LABEL_300;
  instance_9 = instance;
  if ( !*(_DWORD *)(instance + 24) )
    goto LABEL_301;
  *(_QWORD *)(instance + 32) = StringLiteral_5711;
  instance = *v43;
  if ( !*v43 )
    goto LABEL_300;
  HIDWORD(v146) = UnitAsset__get_id(instance, 0LL);
  v47 = System_Int32__ToString((char *)&v146 + 4, 0LL);
  n2_3 = *(_DWORD *)(instance_9 + 24);
  if ( n2_3 <= 1 )
    goto LABEL_301;
  *(_QWORD *)(instance_9 + 40) = v47;
  if ( n2_3 == 2 )
    goto LABEL_301;
  *(_QWORD *)(instance_9 + 48) = StringLiteral_469;
  HIDWORD(v146) = Enemy__MHIGCKCCOID(a1);
  v49 = System_Int32__ToString((char *)&v146 + 4, 0LL);
  n3_3 = *(_DWORD *)(instance_9 + 24);
  if ( n3_3 <= 3 )
    goto LABEL_301;
  *(_QWORD *)(instance_9 + 56) = v49;
  if ( n3_3 == 4 )
    goto LABEL_301;
  *(_QWORD *)(instance_9 + 64) = StringLiteral_300;
  instance = *v43;
  if ( !*v43 )
    goto LABEL_300;
  HIDWORD(v146) = UnitAsset__get_moveSpeed(instance, 0LL);
  v51 = System_Int32__ToString((char *)&v146 + 4, 0LL);
  if ( *(_DWORD *)(instance_9 + 24) <= 5u )
LABEL_301:
    sub_184E63C();
  *(_QWORD *)(instance_9 + 72) = v51;
  instance = System_String__Concat_60662448(instance_9, 0LL);
  if ( !v45 )
    goto LABEL_300;
  SFSController__SendWarn(v45, instance, StringLiteral_1);
LABEL_98:
  instance = UnityEngine_Component__get_gameObject(a1, 0LL);
  if ( !instance )
    goto LABEL_300;
  if ( (unsigned int)UnityEngine_GameObject__get_layer(instance, 0LL) != 21 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    instance = (__int64)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      instance = (__int64)SFSController_TypeInfo;
    }
    if ( !*v43 )
      goto LABEL_300;
    v52 = **(_QWORD **)(instance + 184);
    HIDWORD(v146) = UnitAsset__get_id(*v43, 0LL);
    v53 = System_Int32__ToString((char *)&v146 + 4, 0LL);
    instance = UnityEngine_Component__get_gameObject(a1, 0LL);
    if ( !instance )
      goto LABEL_300;
    HIDWORD(v146) = UnityEngine_GameObject__get_layer(instance, 0LL);
    v54 = System_Int32__ToString((char *)&v146 + 4, 0LL);
    instance = System_String__Concat_60662184(StringLiteral_5710, v53, StringLiteral_253, v54, 0LL);
    if ( !v52 )
      goto LABEL_300;
    SFSController__SendWarn(v52, instance, StringLiteral_1);
  }
  v12 = 1;
LABEL_111:
  CodeStage_AntiCheat_ObscuredTypes_ObscuredFloat__op_Implicit(&v135, 0LL, 0.0);
  v55 = v135;
  v138 = v136;
  v137 = v135;
  *(_DWORD *)(a1 + 552) = v136;
  *(_OWORD *)(a1 + 536) = v55;
  CodeStage_AntiCheat_ObscuredTypes_ObscuredFloat__op_Implicit(&v133, 0LL, 0.0);
  v56 = v133;
  v136 = v134;
  v135 = v133;
  *(_DWORD *)(a1 + 572) = v134;
  *(_OWORD *)(a1 + 556) = v56;
  instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(0LL, 0LL);
  *(_QWORD *)(a1 + 88) = instance;
  *(_DWORD *)(a1 + 96) = v57;
  v58 = *(_QWORD *)(a1 + 168);
  if ( !byte_490B7D4 )
  {
    instance = sub_184E408(&UnityEngine_Vector2_TypeInfo);
    byte_490B7D4 = 1;
  }
  if ( !v58 )
    goto LABEL_300;
  UnityEngine_Rigidbody2D__set_velocity(
    v58,
    0LL,
    **((float **)UnityEngine_Vector2_TypeInfo + 23),
    *(float *)(*((_QWORD *)UnityEngine_Vector2_TypeInfo + 23) + 4LL));
  if ( !v12 )
    goto LABEL_127;
  v59 = *(_QWORD *)(a1 + 64);
  if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
  if ( (UnityEngine_Object__op_Inequality(v59, 0LL, 0LL) & 1) == 0 )
    goto LABEL_127;
  instance = Unit__EEKEPNEBEHH(a1, 0LL);
  v60 = *(_QWORD *)(a1 + 64);
  if ( !v60 )
    goto LABEL_300;
  instance = FastPoolManager__GetPool_46209116(*(unsigned int *)(v60 + 464), 0LL, 0LL, 0LL);
  if ( !instance )
    goto LABEL_300;
  v61 = FastPool__PPDJLCMNLCI(instance, 0LL, 0LL);
  if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
  instance = UnityEngine_Object__op_Inequality(v61, 0LL, 0LL);
  if ( (instance & 1) == 0 )
    goto LABEL_127;
  if ( !v61
    || (instance = UnityEngine_GameObject__get_transform(v61, 0LL), !*(_QWORD *)(a1 + 136))
    || (instance_10 = instance, v63 = UnityEngine_Transform__get_position(*(_QWORD *)(a1 + 136), 0LL), !instance_10) )
  {
LABEL_300:
    sub_184E634(instance);
  }
  UnityEngine_Transform__set_position(instance_10, 0LL, v63);
LABEL_127:
  v64 = *(_QWORD *)(a1 + 152);
  if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);
  if ( (UnityEngine_Object__op_Inequality(v64, 0LL, 0LL) & 1) == 0 )
    return Enemy__HGMEHOONOCO(a1);
  v65 = DG_Tweening_ShortcutExtensions43__DOFade(*(_QWORD *)(a1 + 152), 0LL, 0.0, 0.5);
  v66 = sub_184E624(DG_Tweening_TweenCallback_TypeInfo);
  DG_Tweening_TweenCallback___ctor(v66, a1, Method_Enemy_HGMEHOONOCO__, 0LL);
  return DG_Tweening_TweenSettingsExtensions__OnComplete_object_(
           v65,
           v66,
           Method_DG_Tweening_TweenSettingsExtensions_OnComplete_Tweener___);
}