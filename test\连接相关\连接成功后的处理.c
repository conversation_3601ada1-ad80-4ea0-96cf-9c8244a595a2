/**
 * SFS连接成功后的处理函数 - 收集设备信息并发送登录请求
 *
 * 功能描述：
 * 这是SmartFoxServer连接成功后的核心处理函数，负责收集客户端的详细信息并构建登录请求。
 * 该函数会收集大量的设备、应用、用户和游戏状态信息，用于服务器端的验证和统计。
 *
 * 主要收集的信息包括：
 * 1. 设备信息：设备ID、电池状态、处理器信息等
 * 2. 应用信息：版本号、包名、安装器等
 * 3. 用户信息：用户名、游戏时长、配置等
 * 4. 游戏状态：存档数据、购买记录、反作弊信息等
 * 5. 安全信息：各种反作弊检查结果
 *
 * @param a1 SFS控制器实例指针，包含连接配置和状态信息
 * @return __int64 操作结果：成功返回发送结果，失败返回0
 *
 * 数据字段说明：
 * - StringLiteral_15120: 设备唯一标识符键名
 * - StringLiteral_19830: 应用版本键名
 * - StringLiteral_14464: 应用标识符键名
 * - StringLiteral_16657: 安装器名称键名
 * - StringLiteral_17872: 用户信息键名
 * - StringLiteral_16325: 用户状态标识键名
 * - StringLiteral_14092: 游戏时长键名
 * - 以及大量其他游戏数据字段...
 */
__int64 __fastcall SFSController__HCAJEPAGHLL(__int64 a1)
{
  __int64 result; // x0
  __int64 v3; // x20 - SFS对象实例
  _QWORD *deviceUniqueIdentifier; // x0 - 设备唯一标识符
  __int64 version; // x0 - 应用版本
  __int64 identifier; // x0 - 应用标识符
  __int64 installerName; // x0 - 安装器名称
  __int64 v8; // x8
  __int64 StringLiteral_17872; // x21 - 字符串字面量
  __int64 v10; // x8
  __int64 *v11; // x8
  char FukNut; // w0 - 反作弊检查结果
  void *StringLiteral_16325; // x21
  _QWORD *v14; // x8
  __int64 StringLiteral_1; // x0
  void *StringLiteral; // x2
  __int64 v17; // x8
  unsigned int Platform; // w0 - 平台类型
  __int64 String; // x0
  char v20; // w0
  void *StringLiteral_16354; // x21
  _QWORD *v22; // x8
  _QWORD *NetworkManager_TypeInfo; // x0
  __int64 v24; // x0
  _QWORD *GameController_TypeInfo; // x0
  char v26; // w0
  __int64 v27; // x8
  unsigned int SFSSession; // w0 - SFS会话ID
  __int64 aDid; // x0 - 广告ID
  unsigned int PollFished; // w0 - 投票钓鱼状态
  char GeneratedH; // w0 - 生成的哈希值
  __int64 v32; // x0
  char v33; // w0
  __int64 v34; // x0
  __int64 v35; // x0
  __int64 v36; // x0
  unsigned int v37; // w0
  unsigned int processorCount; // w0 - 处理器核心数
  __int64 v39; // x22
  __int64 v40; // x23
  __int64 v41; // x24
  __int64 v42; // x0
  __int64 v43; // x21
  __int64 v44; // x0
  unsigned int v45; // w0
  char v46; // w0
  __int64 v47; // x8
  char v48; // w0
  char UFO; // w0 - UFO检查标志
  char v50; // w0
  char TradeBanWord; // w0 - 交易封禁词检查
  char v52; // w0
  __int64 v53; // x0
  __int64 object; // x0
  __int64 Sign; // x0 - 签名
  __int64 FFs; // x0 - FFs值
  __int64 FFz; // x0 - FFz值
  __int64 UID; // x0 - 用户ID
  unsigned int Session; // w0 - 会话ID
  __int64 v60; // x0
  unsigned int PurchasedGems; // w0 - 已购买宝石数量
  __int64 v62; // x19 - SmartFox连接实例
  __int64 v63; // x22 - 用户名
  __int64 v64; // x21 - 登录请求对象
  _QWORD v65[2]; // [xsp+0h] [xbp-80h] BYREF
  int batteryStatus; // [xsp+10h] [xbp-70h] - 电池状态
  double v67; // [xsp+18h] [xbp-68h] BYREF - 游戏时长

  // 静态初始化检查 - 确保类型信息只初始化一次
  if ( (byte_490BBB1 & 1) == 0 )
  {
    // 初始化所有需要的类型信息和字符串字面量
    sub_184E408(&UnityEngine_Application_TypeInfo);
    sub_184E408(&UnityEngine_BatteryStatus_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&Helpers_TypeInfo);
    sub_184E408(&Sfs2X_Requests_LoginRequest_TypeInfo);
    sub_184E408(&NetworkManager_TypeInfo);
    sub_184E408(&Method_CodeStage_AntiCheat_Storage_ObscuredPrefs_Get_string___);
    sub_184E408(&CodeStage_AntiCheat_Storage_ObscuredPrefs_TypeInfo);
    sub_184E408(&SFSController_TypeInfo);
    sub_184E408(&Sfs2X_Entities_Data_SFSObject_TypeInfo);
    // 初始化大量字符串字面量（用于数据字段名称）
    sub_184E408(&StringLiteral_19883);
    sub_184E408(&StringLiteral_18277);
    sub_184E408(&StringLiteral_13959);
    sub_184E408(&StringLiteral_16325);
    sub_184E408(&StringLiteral_18497);
    sub_184E408(&StringLiteral_14092);
    sub_184E408(&StringLiteral_16278);
    sub_184E408(&StringLiteral_19371);
    sub_184E408(&StringLiteral_15382);
    sub_184E408(&StringLiteral_15867);
    sub_184E408(&StringLiteral_16657);
    sub_184E408(&StringLiteral_17341);
    sub_184E408(&StringLiteral_18823);
    sub_184E408(&StringLiteral_16275);
    sub_184E408(&StringLiteral_14070);
    sub_184E408(&StringLiteral_15388);
    sub_184E408(&StringLiteral_19900);
    sub_184E408(&StringLiteral_14307);
    sub_184E408(&StringLiteral_16354);
    sub_184E408(&StringLiteral_18088);
    sub_184E408(&StringLiteral_14464);
    sub_184E408(&StringLiteral_18712);
    sub_184E408(&StringLiteral_17735);
    sub_184E408(&StringLiteral_19781);
    sub_184E408(&StringLiteral_17646);
    sub_184E408(&StringLiteral_19341);
    sub_184E408(&StringLiteral_19340);
    sub_184E408(&StringLiteral_17971);
    sub_184E408(&StringLiteral_19342);
    sub_184E408(&StringLiteral_18483);
    sub_184E408(&StringLiteral_17934);
    sub_184E408(&StringLiteral_19343);
    sub_184E408(&StringLiteral_18254);
    sub_184E408(&StringLiteral_18644);
    sub_184E408(&StringLiteral_19339);
    sub_184E408(&StringLiteral_20083);
    sub_184E408(&StringLiteral_18332);
    sub_184E408(&StringLiteral_20128);
    sub_184E408(&StringLiteral_19830);
    sub_184E408(&StringLiteral_15047);
    sub_184E408(&StringLiteral_15120);
    sub_184E408(&StringLiteral_8533);
    sub_184E408(&StringLiteral_18311);
    sub_184E408(&StringLiteral_18478);
    sub_184E408(&StringLiteral_18770);
    sub_184E408(&StringLiteral_17520);
    sub_184E408(&StringLiteral_19130);
    sub_184E408(&StringLiteral_19051);
    sub_184E408(&StringLiteral_17592);
    sub_184E408(&StringLiteral_19370);
    sub_184E408(&StringLiteral_9185);
    sub_184E408(&StringLiteral_1);
    sub_184E408(&StringLiteral_18705);
    sub_184E408(&StringLiteral_15877);
    sub_184E408(&StringLiteral_15370);
    sub_184E408(&StringLiteral_17872);
    sub_184E408(&StringLiteral_2043);
    sub_184E408(&StringLiteral_14323);
    sub_184E408(&StringLiteral_19018);
    sub_184E408(&StringLiteral_1970);
    byte_490BBB1 = 1; // 标记已初始化
  }

  // === 第一步：获取SmartFox连接实例并验证 ===
  result = *(_QWORD *)(a1 + 64);
  if ( result )
  {
    // 设置连接状态为已连接
    SmartFoxConnection__set_Connection(result, 0LL);

    // === 第二步：创建登录数据容器 ===
    // 创建SFS数据对象，用于存储所有登录相关信息
    v3 = sub_184E624(Sfs2X_Entities_Data_SFSObject_TypeInfo);
    Sfs2X_Entities_Data_SFSObject___ctor(v3, 0LL);

    // === 第三步：收集基础设备信息 ===
    // 获取设备唯一标识符（用于设备识别和防作弊）
    deviceUniqueIdentifier = (_QWORD *)UnityEngine_SystemInfo__get_deviceUniqueIdentifier(0LL);
    if ( v3 )
    {
      // 添加设备唯一标识符到登录数据
      Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_15120, deviceUniqueIdentifier, 0LL);

      // === 第四步：收集应用基础信息 ===
      // 获取并添加应用版本信息（用于版本兼容性检查）
      if ( !*((_DWORD *)UnityEngine_Application_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(UnityEngine_Application_TypeInfo);
      version = UnityEngine_Application__get_version(0LL);
      Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_19830, version, 0LL);

      // 获取并添加应用包标识符（用于应用合法性验证）
      identifier = UnityEngine_Application__get_identifier(0LL);
      Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_14464, identifier, 0LL);

      // 获取并添加安装器名称（用于检测应用来源）
      installerName = UnityEngine_Application__get_installerName(0LL);
      Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_16657, installerName, 0LL);
      // === 第五步：收集用户账户信息 ===
      // 获取游戏控制器实例
      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
      deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
      if ( deviceUniqueIdentifier )
      {
        // 获取用户相关信息（偏移33*8=264字节处存储用户数据指针）
        v8 = deviceUniqueIdentifier[33];
        if ( v8 )
        {
          StringLiteral_17872 = StringLiteral_17872;
          // 检查用户信息是否存在（偏移56字节处存储用户名或ID）
          if ( *(_QWORD *)(v8 + 56) )
          {
            // 重新获取游戏控制器实例以确保数据一致性
            if ( !*((_DWORD *)GameController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
            if ( !deviceUniqueIdentifier )
              goto LABEL_96;
            v10 = deviceUniqueIdentifier[33];
            if ( !v10 )
              goto LABEL_96;
            v11 = (__int64 *)(v10 + 56); // 获取实际用户信息
          }
          else
          {
            v11 = &StringLiteral_8533; // 使用默认值（可能是"Guest"或空字符串）
          }
          // 添加用户信息到登录数据
          Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_17872, *v11, 0LL);

          // === 第六步：执行反作弊检查 ===
          // 进行第一层反作弊检查
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
          if ( deviceUniqueIdentifier )
          {
            FukNut = GameController__get_FukNut(deviceUniqueIdentifier, 0LL);  // 检查特定作弊标志
            StringLiteral_16325 = StringLiteral_16325;
            // 检查是否为作弊用户（第一种检测方法）
            if ( (FukNut & 1) != 0 )
              goto LABEL_26;

            // 进行第二层反作弊检查
            if ( !*((_DWORD *)GameController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
            if ( !deviceUniqueIdentifier )
              goto LABEL_96;

            // 检查是否为作弊用户（第二种检测方法）
            if ( (GameController__Hacker(deviceUniqueIdentifier, 0LL) & 1) != 0 )
LABEL_26:
              v14 = &StringLiteral_2043; // 标记为作弊用户（可能是"HACKER"或"CHEAT"）
            else
              v14 = &StringLiteral_1970; // 正常用户（可能是"NORMAL"或"CLEAN"）

            // 添加用户安全状态标识到登录数据
            Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_16325, *v14, 0LL);

            // 获取游戏时长信息
            if ( !*((_DWORD *)GameController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
            if ( deviceUniqueIdentifier )
            {
              v67 = GameController__HowOldAreYou(deviceUniqueIdentifier, 0LL);
              StringLiteral_1 = System_Double__ToString(&v67, 0LL);
              if ( StringLiteral_1 )
                StringLiteral = (void *)StringLiteral_1;
              else
                StringLiteral = StringLiteral_1;
              // 添加游戏时长到登录数据
              Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_14092, StringLiteral, 0LL);

              if ( !*((_DWORD *)GameController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
              deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
              if ( deviceUniqueIdentifier )
              {
                // 获取用户配置信息
                v17 = deviceUniqueIdentifier[17];
                if ( v17 )
                {
                  // 添加用户配置到登录数据
                  Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_17341, *(_QWORD *)(v17 + 56), 0LL);

                  // 添加平台信息
                  Platform = GameController__get_Platform(0LL);
                  Sfs2X_Entities_Data_SFSObject__PutByte(v3, StringLiteral_18311, Platform, 0LL);

                  // 获取并添加玩家偏好设置
                  String = UnityEngine_PlayerPrefs__GetString(StringLiteral_9185, StringLiteral_19781, 0LL);
                  Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_18770, String, 0LL);

                  // 添加反作弊系统的布尔值
                  v20 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                          *(_QWORD *)(a1 + 752),
                          *(unsigned int *)(a1 + 760),
                          0LL);
                  Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_17520, v20 & 1, 0LL);
                  deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                  if ( deviceUniqueIdentifier )
                  {
                    StringLiteral_16354 = StringLiteral_16354;
                    v22 = &StringLiteral_1;
                    if ( deviceUniqueIdentifier[36] )
                    {
                      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
                        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
                      deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                      if ( !deviceUniqueIdentifier )
                        goto LABEL_96;
                      v22 = deviceUniqueIdentifier + 36;
                    }
                    Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_16354, *v22, 0LL);
                    NetworkManager_TypeInfo = NetworkManager_TypeInfo;
                    if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
                    {
                      j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
                      NetworkManager_TypeInfo = NetworkManager_TypeInfo;
                    }
                    v24 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                            *(_QWORD *)(NetworkManager_TypeInfo[23] + 24LL),
                            0LL);
                    Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_18705, v24, 0LL);
                    GameController_TypeInfo = GameController_TypeInfo;
                    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
                    {
                      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
                      GameController_TypeInfo = GameController_TypeInfo;
                    }
                    v26 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                            *(_QWORD *)(GameController_TypeInfo[23] + 124LL),
                            *(unsigned int *)(GameController_TypeInfo[23] + 132LL),
                            0LL);
                    Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_15388, v26 & 1, 0LL);
                    deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                    if ( deviceUniqueIdentifier )
                    {
                      deviceUniqueIdentifier = (_QWORD *)Sfs2X_Entities_Data_SFSObject__PutByte(
                                                           v3,
                                                           StringLiteral_20083,
                                                           *((unsigned int *)deviceUniqueIdentifier + 68),
                                                           0LL);
                      if ( !byte_490BC64 )
                      {
                        deviceUniqueIdentifier = (_QWORD *)sub_184E408(&Boom_TypeInfo);
                        byte_490BC64 = 1;
                      }
                      v27 = **((_QWORD **)Boom_TypeInfo + 23);
                      if ( v27 )
                      {
                        Sfs2X_Entities_Data_SFSObject__PutBool(
                          v3,
                          StringLiteral_18478,
                          *(unsigned __int8 *)(v27 + 32),
                          0LL);
                        if ( !byte_490BC65 )
                        {
                          sub_184E408(&SaveController_TypeInfo);
                          byte_490BC65 = 1;
                        }
                        deviceUniqueIdentifier = (_QWORD *)**((_QWORD **)SaveController_TypeInfo + 23);
                        if ( deviceUniqueIdentifier )
                        {
                          SFSSession = SaveController__GetSFSSession(deviceUniqueIdentifier, 0LL);
                          Sfs2X_Entities_Data_SFSObject__PutInt(v3, StringLiteral_19130, SFSSession, 0LL);
                          deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                          if ( deviceUniqueIdentifier )
                          {
                            aDid = GameController__get_aDid(deviceUniqueIdentifier, 0LL);
                            Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_13959, aDid, 0LL);
                            if ( !byte_490BC65 )
                            {
                              sub_184E408(&SaveController_TypeInfo);
                              byte_490BC65 = 1;
                            }
                            deviceUniqueIdentifier = (_QWORD *)**((_QWORD **)SaveController_TypeInfo + 23);
                            if ( deviceUniqueIdentifier )
                            {
                              PollFished = SaveController__GetPollFished(deviceUniqueIdentifier, 0LL);
                              Sfs2X_Entities_Data_SFSObject__PutInt(v3, StringLiteral_18277, PollFished, 0LL);
                              deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                              if ( deviceUniqueIdentifier )
                              {
                                GeneratedH = GameController__get_GeneratedH(deviceUniqueIdentifier, 0LL);
                                Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_16275, GeneratedH & 1, 0LL);
                                UnityEngine_SystemInfo__get_batteryLevel(0LL);
                                Sfs2X_Entities_Data_SFSObject__PutFloat(v3, StringLiteral_14307, 0LL);
                                batteryStatus = UnityEngine_SystemInfo__get_batteryStatus(0LL);
                                v65[0] = UnityEngine_BatteryStatus_TypeInfo;
                                v65[1] = -1LL;
                                v32 = System_Enum__ToString(v65, 0LL);
                                Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_14323, v32, 0LL);
                                v33 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                                        *(_QWORD *)(a1 + 692),
                                        *(unsigned int *)(a1 + 700),
                                        0LL);
                                Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_19883, v33 & 1, 0LL);
                                v34 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                        *(_QWORD *)(a1 + 704),
                                        0LL);
                                Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_18088, v34, 0LL);
                                v35 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                        *(_QWORD *)(a1 + 712),
                                        0LL);
                                Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_17592, v35, 0LL);
                                v36 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                        *(_QWORD *)(a1 + 720),
                                        0LL);
                                Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_15382, v36, 0LL);
                                Sfs2X_Entities_Data_SFSObject__PutByte(
                                  v3,
                                  StringLiteral_15370,
                                  *(unsigned __int8 *)(a1 + 728),
                                  0LL);
                                Sfs2X_Entities_Data_SFSObject__PutByte(
                                  v3,
                                  StringLiteral_19051,
                                  *(unsigned __int8 *)(a1 + 729),
                                  0LL);
                                v37 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                                        *(_QWORD *)(a1 + 276),
                                        *(_QWORD *)(a1 + 284));
                                Sfs2X_Entities_Data_SFSObject__PutInt(v3, StringLiteral_17735, v37, 0LL);
                                processorCount = UnityEngine_SystemInfo__get_processorCount(0LL);
                                Sfs2X_Entities_Data_SFSObject__PutInt(v3, StringLiteral_18254, processorCount, 0LL);
                                v39 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                        *(_QWORD *)(a1 + 704),
                                        0LL);
                                v40 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                        *(_QWORD *)(a1 + 712),
                                        0LL);
                                v41 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                        *(_QWORD *)(a1 + 720),
                                        0LL);
                                v42 = System_Byte__ToString(a1 + 729, 0LL);
                                v43 = System_String__Concat_60662184(v39, v40, v41, v42, 0LL);
                                if ( !*((_DWORD *)Helpers_TypeInfo + 56) )
                                  j_il2cpp_runtime_class_init_0(Helpers_TypeInfo);
                                v44 = Helpers__Md5Sum(v43, 0LL);
                                Sfs2X_Entities_Data_SFSObject__PutUtfString(v3, StringLiteral_17646, v44, 0LL);
                                deviceUniqueIdentifier = (_QWORD *)InstallRefApi__get_Instance(0LL);
                                if ( deviceUniqueIdentifier )
                                {
                                  Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                    v3,
                                    StringLiteral_18483,
                                    deviceUniqueIdentifier[6],
                                    0LL);
                                  deviceUniqueIdentifier = (_QWORD *)InstallRefApi__get_Instance(0LL);
                                  if ( deviceUniqueIdentifier )
                                  {
                                    Sfs2X_Entities_Data_SFSObject__PutLong(
                                      v3,
                                      StringLiteral_18497,
                                      deviceUniqueIdentifier[7],
                                      0LL);
                                    deviceUniqueIdentifier = (_QWORD *)InstallRefApi__get_Instance(0LL);
                                    if ( deviceUniqueIdentifier )
                                    {
                                      Sfs2X_Entities_Data_SFSObject__PutLong(
                                        v3,
                                        StringLiteral_18644,
                                        deviceUniqueIdentifier[8],
                                        0LL);
                                      deviceUniqueIdentifier = (_QWORD *)AdManager__get_instance(0LL);
                                      if ( deviceUniqueIdentifier )
                                      {
                                        v45 = CodeStage_AntiCheat_ObscuredTypes_ObscuredShort__op_Implicit_26304492(
                                                *(_QWORD *)((char *)deviceUniqueIdentifier + 116),
                                                0LL);
                                        Sfs2X_Entities_Data_SFSObject__PutShort(v3, StringLiteral_14070, v45, 0LL);
                                        deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                        if ( deviceUniqueIdentifier )
                                        {
                                          v46 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                                                  deviceUniqueIdentifier[52],
                                                  *((unsigned int *)deviceUniqueIdentifier + 106),
                                                  0LL);
                                          Sfs2X_Entities_Data_SFSObject__PutBool(v3, StringLiteral_19900, v46 & 1, 0LL);
                                          deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                          if ( deviceUniqueIdentifier )
                                          {
                                            v47 = deviceUniqueIdentifier[29];
                                            if ( v47 )
                                            {
                                              Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                v3,
                                                StringLiteral_16278,
                                                *(_QWORD *)(v47 + 96),
                                                0LL);
                                              Sfs2X_Entities_Data_SFSObject__PutByte(
                                                v3,
                                                StringLiteral_15047,
                                                *(unsigned __int8 *)(a1 + 676),
                                                0LL);
                                              deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                              if ( deviceUniqueIdentifier )
                                              {
                                                v48 = GameController__Hacker(deviceUniqueIdentifier, 0LL);
                                                Sfs2X_Entities_Data_SFSObject__PutBool(
                                                  v3,
                                                  StringLiteral_19339,
                                                  v48 & 1,
                                                  0LL);
                                                UFO = GameController__get_UFO(0LL);
                                                Sfs2X_Entities_Data_SFSObject__PutBool(
                                                  v3,
                                                  StringLiteral_19340,
                                                  UFO & 1,
                                                  0LL);
                                                v50 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                                                        *(_QWORD *)(*((_QWORD *)GameController_TypeInfo + 23) + 40LL),
                                                        *(unsigned int *)(*((_QWORD *)GameController_TypeInfo + 23)
                                                                        + 48LL),
                                                        0LL);
                                                Sfs2X_Entities_Data_SFSObject__PutBool(
                                                  v3,
                                                  StringLiteral_19341,
                                                  v50 & 1,
                                                  0LL);
                                                deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                                if ( deviceUniqueIdentifier )
                                                {
                                                  TradeBanWord = GameController__get_TradeBanWord(
                                                                   deviceUniqueIdentifier,
                                                                   0LL);
                                                  Sfs2X_Entities_Data_SFSObject__PutBool(
                                                    v3,
                                                    StringLiteral_19342,
                                                    TradeBanWord & 1,
                                                    0LL);
                                                  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                                                    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                                                  if ( !byte_490BC66 )
                                                  {
                                                    sub_184E408(&SFSController_TypeInfo);
                                                    byte_490BC66 = 1;
                                                  }
                                                  deviceUniqueIdentifier = SFSController_TypeInfo;
                                                  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                                                  {
                                                    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                                                    deviceUniqueIdentifier = SFSController_TypeInfo;
                                                  }
                                                  if ( *(_QWORD *)deviceUniqueIdentifier[23] )
                                                  {
                                                    v52 = CodeStage_AntiCheat_ObscuredTypes_ObscuredByte__op_Implicit_26302544(
                                                            *(unsigned int *)(*(_QWORD *)deviceUniqueIdentifier[23]
                                                                            + 392LL) | ((unsigned __int64)*(unsigned __int8 *)(*(_QWORD *)deviceUniqueIdentifier[23] + 396LL) << 32),
                                                            0LL);
                                                    Sfs2X_Entities_Data_SFSObject__PutBool(
                                                      v3,
                                                      StringLiteral_19343,
                                                      v52 == 1,
                                                      0LL);
                                                    Sfs2X_Entities_Data_SFSObject__PutBool(
                                                      v3,
                                                      StringLiteral_17934,
                                                      *(unsigned __int8 *)(*((_QWORD *)GameController_TypeInfo + 23)
                                                                         + 36LL),
                                                      0LL);
                                                    deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                                    if ( deviceUniqueIdentifier )
                                                    {
                                                      v53 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                                              deviceUniqueIdentifier[56],
                                                              0LL);
                                                      Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                        v3,
                                                        StringLiteral_17971,
                                                        v53,
                                                        0LL);
                                                      if ( !*((_DWORD *)CodeStage_AntiCheat_Storage_ObscuredPrefs_TypeInfo
                                                            + 56) )
                                                        j_il2cpp_runtime_class_init_0(CodeStage_AntiCheat_Storage_ObscuredPrefs_TypeInfo);
                                                      object = CodeStage_AntiCheat_Storage_ObscuredPrefs__Get_object_(
                                                                 StringLiteral_19371,
                                                                 StringLiteral_1,
                                                                 Method_CodeStage_AntiCheat_Storage_ObscuredPrefs_Get_string___);
                                                      Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                        v3,
                                                        StringLiteral_19370,
                                                        object,
                                                        0LL);
                                                      deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                                      if ( deviceUniqueIdentifier )
                                                      {
                                                        Sign = GameController__GetSign(deviceUniqueIdentifier, 0LL);
                                                        Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                          v3,
                                                          StringLiteral_19018,
                                                          Sign,
                                                          0LL);
                                                        deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                                        if ( deviceUniqueIdentifier )
                                                        {
                                                          FFs = GameController__GetFFs(deviceUniqueIdentifier, 0LL);
                                                          Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                            v3,
                                                            StringLiteral_15867,
                                                            FFs,
                                                            0LL);
                                                          deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                                          if ( deviceUniqueIdentifier )
                                                          {
                                                            FFz = GameController__GetFFz(deviceUniqueIdentifier, 0LL);
                                                            Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                              v3,
                                                              StringLiteral_15877,
                                                              FFz,
                                                              0LL);
                                                            if ( !byte_490BC65 )
                                                            {
                                                              sub_184E408(&SaveController_TypeInfo);
                                                              byte_490BC65 = 1;
                                                            }
                                                            deviceUniqueIdentifier = (_QWORD *)**((_QWORD **)SaveController_TypeInfo
                                                                                                + 23);
                                                            if ( deviceUniqueIdentifier )
                                                            {
                                                              UID = SaveController__GetUID(deviceUniqueIdentifier, 0LL);
                                                              Sfs2X_Entities_Data_SFSObject__PutUtfString(
                                                                v3,
                                                                StringLiteral_20128,
                                                                UID,
                                                                0LL);
                                                              if ( !byte_490BC65 )
                                                              {
                                                                sub_184E408(&SaveController_TypeInfo);
                                                                byte_490BC65 = 1;
                                                              }
                                                              deviceUniqueIdentifier = (_QWORD *)**((_QWORD **)SaveController_TypeInfo
                                                                                                  + 23);
                                                              if ( deviceUniqueIdentifier )
                                                              {
                                                                // 获取并添加会话ID
                                                                Session = SaveController__GetSession(
                                                                            deviceUniqueIdentifier,
                                                                            0LL);
                                                                Sfs2X_Entities_Data_SFSObject__PutInt(
                                                                  v3,
                                                                  StringLiteral_18823,
                                                                  Session,
                                                                  0LL);

                                                                // 获取并添加额外的游戏数据对象
                                                                v60 = SFSController__GAAPMEDFDFL(a1);
                                                                Sfs2X_Entities_Data_SFSObject__PutSFSObject(
                                                                  v3,
                                                                  StringLiteral_18712,
                                                                  v60,
                                                                  0LL);

                                                                if ( !byte_490BC65 )
                                                                {
                                                                  sub_184E408(&SaveController_TypeInfo);
                                                                  byte_490BC65 = 1;
                                                                }
                                                                deviceUniqueIdentifier = (_QWORD *)**((_QWORD **)SaveController_TypeInfo + 23);
                                                                if ( deviceUniqueIdentifier )
                                                                {
                                                                  // 获取并添加已购买宝石数量
                                                                  PurchasedGems = SaveController__GetPurchasedGems(
                                                                                    deviceUniqueIdentifier,
                                                                                    0LL);
                                                                  Sfs2X_Entities_Data_SFSObject__PutInt(
                                                                    v3,
                                                                    StringLiteral_18332,
                                                                    PurchasedGems,
                                                                    0LL);

                                                                  // 准备发送登录请求
                                                                  v62 = *(_QWORD *)(a1 + 64); // 获取SmartFox连接实例
                                                                  deviceUniqueIdentifier = (_QWORD *)GameController__get_instance(0LL);
                                                                  if ( deviceUniqueIdentifier )
                                                                  {
                                                                    // 获取用户名
                                                                    v63 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                                                            deviceUniqueIdentifier[35],
                                                                            0LL);

                                                                    // 创建登录请求对象
                                                                    v64 = sub_184E624(Sfs2X_Requests_LoginRequest_TypeInfo);
                                                                    deviceUniqueIdentifier = (_QWORD *)Sfs2X_Requests_LoginRequest___ctor(v64, v63, StringLiteral_1, StringLiteral_1, v3, 0LL);

                                                                    // 发送登录请求到服务器
                                                                    if ( v62 )
                                                                      return Sfs2X_SmartFox__Send(v62, v64, 0LL);
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          }
                                                        }
                                                      }
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
LABEL_96:
    // 错误处理：清理资源
    sub_184E634(deviceUniqueIdentifier);
  }
  return result;
}