// 敌人死亡掉落处理函数 - 这是游戏中敌人死亡时的主要掉落逻辑
__int64 __fastcall Enemy__HOHHLKELAFB(__int64 a1)
{
  _QWORD **SFSController_TypeInfo; // x0 - SFSController类型信息
  __int64 n4; // x0
  _QWORD **SFSController_TypeInfo_1; // x0
  _QWORD **SFSController_TypeInfo_2; // x0
  __int64 v6; // x0
  __int64 v7; // x1
  _QWORD **NetworkManager_TypeInfo; // x0
  __int64 v9; // x8
  __int64 *v10; // x27
  __int64 v11; // x21
  int id; // w0
  _QWORD **SFSController_TypeInfo_3; // x0
  __int64 v14; // x21
  __int64 n4_9; // x22
  __int64 v16; // x0
  __int64 v17; // x21
  long double position; // q0 - 敌人位置坐标
  long double v19; // q1
  long double v20; // q2
  __int64 v21; // x8
  __int64 v22; // x22
  long double position_1; // q8
  long double v24; // q9
  long double v25; // q10
  unsigned int Quantity; // w23 - 物品数量
  unsigned int v27; // w24
  unsigned int v28; // w25
  __int64 v29; // x8
  __int64 (__fastcall **v30)(); // x28
  _QWORD **SFSController_TypeInfo_4; // x0
  unsigned int n4_1; // w21
  int maxLoot; // w0 - 最大掉落数量
  __int64 n4_2; // x1
  _QWORD **SFSController_TypeInfo_5; // x0
  __int64 v36; // x21
  __int64 v37; // x0
  _QWORD **SFSController_TypeInfo_6; // x0
  __int64 v39; // x8
  __int64 n4_3; // x21
  __int64 v41; // x9
  int *v42; // x10
  __int64 v43; // x0
  __int64 v44; // x21
  __int64 *v45; // x8
  __int64 v46; // x22
  __int64 v47; // x8
  __int64 v48; // x9
  int *v49; // x10
  __int64 v50; // x0
  __int64 v51; // x8
  __int64 v52; // x9
  int *v53; // x10
  __int64 v54; // x0
  __int64 n4_4; // x23
  __int64 v56; // x10
  __int64 v57; // x8
  __int64 v58; // x24
  _QWORD **SFSController_TypeInfo_7; // x0
  __int64 v60; // x24
  unsigned int n323345; // w24 - 物品ID
  __int64 n4_7; // x25
  char v63; // w25
  _QWORD *v64; // x8
  __int64 v65; // x26
  _QWORD *v66; // x8
  __int64 v67; // x24
  __int64 v68; // x24
  long double v69; // q8
  long double v70; // q1
  long double v71; // q9
  long double v72; // q2
  long double v73; // q10
  unsigned int v74; // w25
  __int64 v75; // x23
  unsigned int n4_8; // w26
  unsigned int v77; // w28
  __int64 v78; // x8
  __int64 v79; // x23
  unsigned int v80; // w25
  __int64 v81; // x23
  int v82; // w1
  int v83; // w24
  _QWORD *GameController_TypeInfo; // x0
  __int64 v85; // x8
  __int64 *v86; // x8
  unsigned int v87; // w23
  __int64 v88; // x23
  _QWORD *GameController_TypeInfo_1; // x0
  int v90; // w1
  int v91; // w24
  __int64 v92; // x8
  _QWORD **SFSController_TypeInfo_8; // x0
  __int64 v94; // x19
  __int64 n4_5; // x20
  __int64 v96; // x0
  __int64 n4_6; // x19
  __int64 v98; // x0
  __int64 v99; // x0
  __int64 v100; // x19
  __int64 v101; // x0
  int v102; // [xsp+8h] [xbp-B8h]
  unsigned int v103; // [xsp+20h] [xbp-A0h]
  unsigned int v104; // [xsp+24h] [xbp-9Ch]
  unsigned __int64 v105; // [xsp+28h] [xbp-98h]
  __int64 v106; // [xsp+38h] [xbp-88h] BYREF
  __int64 n9; // [xsp+48h] [xbp-78h] BYREF

  // 初始化检查 - 确保所有必要的类型信息已加载
  if ( (byte_490D09A & 1) == 0 )
  {
    // 初始化Unity引擎和游戏相关的类型信息
    sub_184E408(&UnityEngine_Application_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&System_Collections_Generic_IEnumerable_IEPDBNJKNLM__TypeInfo);
    sub_184E408(&System_Collections_Generic_IEnumerator_IEPDBNJKNLM__TypeInfo);
    sub_184E408(&System_Collections_IEnumerator_TypeInfo);
    sub_184E408(&MHLCNCDHBNN_TypeInfo);
    sub_184E408(&NetworkManager_TypeInfo);
    sub_184E408(&UnityEngine_Object_TypeInfo);
    sub_184E408(&SFSController_TypeInfo);
    // 初始化字符串字面量
    sub_184E408(&StringLiteral_7865);
    sub_184E408(&StringLiteral_19624);
    sub_184E408(&StringLiteral_299);
    sub_184E408(&StringLiteral_8498);
    sub_184E408(&StringLiteral_19626);
    sub_184E408(&StringLiteral_17568);
    sub_184E408(&StringLiteral_3894);
    sub_184E408(&StringLiteral_3357);
    sub_184E408(&StringLiteral_1);
    sub_184E408(&StringLiteral_3749);
    sub_184E408(&StringLiteral_6372);
    byte_490D09A = 1; // 标记初始化完成
  }
  n9 = 0LL;
  v106 = 0LL;

  // 反作弊检查 - 检查敌人是否已经死亡（防止重复击杀）
  if ( (unsigned int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                       *(_QWORD *)(a1 + 176),  // 敌人死亡状态标志
                       *(_QWORD *)(a1 + 184),
                       0LL) )
  {
    // 初始化SFS控制器（服务器通信控制器）
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    }
    n4 = *SFSController_TypeInfo[23];
    if ( !n4 )
      goto LABEL_236;

    // 记录可能的作弊行为（重复击杀）
    SFSController__LogHack(n4, StringLiteral_8498, 0LL);

    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
    }
    n4 = *SFSController_TypeInfo_1[23];
    if ( !n4 )
      goto LABEL_236;

    // 检查服务器连接状态，如果未连接则退出
    n4 = SFSController__get_Connected(n4, 0LL);
    if ( (n4 & 1) == 0 )
      return n4;
  }
  // 检查敌人等级是否过高（>=4级），防止高级敌人被低级玩家击杀
  if ( (int)CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
              *(_QWORD *)(a1 + 248),  // 敌人等级
              *(_QWORD *)(a1 + 256),
              0LL) >= 4 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
    }
    n4 = *SFSController_TypeInfo_2[23];
    if ( !n4 )
      goto LABEL_236;
    // 检查服务器连接，高级敌人需要服务器验证
    n4 = SFSController__get_Connected(n4, 0LL);
    if ( (n4 & 1) == 0 )
      return n4;
  }

  // 生成新的随机死亡ID，防止重复处理
  v6 = UnityEngine_Random__Range_69543328(1LL, 99999LL, 0LL);
  *(_QWORD *)(a1 + 176) = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit(v6, 0LL);
  *(_QWORD *)(a1 + 184) = v7;
  // 检查网络管理器状态
  NetworkManager_TypeInfo = (_QWORD **)NetworkManager_TypeInfo;
  if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
    NetworkManager_TypeInfo = (_QWORD **)NetworkManager_TypeInfo;
  }

  // 检查是否为多人游戏模式
  n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
         *NetworkManager_TypeInfo[23],
         *((unsigned int *)NetworkManager_TypeInfo[23] + 2),
         0LL);
  if ( (n4 & 1) == 0 )  // 如果不是多人模式
  {
    // 检查敌人是否有掉落物品
    n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
           *(_QWORD *)(a1 + 88),   // 敌人掉落标志
           *(unsigned int *)(a1 + 96),
           0LL);
    if ( (n4 & 1) != 0 )  // 如果有掉落物品
    {
      v10 = (__int64 *)(a1 + 64);  // 获取敌人单位资产指针
      v9 = *(_QWORD *)(a1 + 64);
      if ( !v9 )
        goto LABEL_236;
      v11 = *(_QWORD *)(v9 + 552);  // 获取物品资产
      if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);

      // 检查物品资产是否有效
      n4 = UnityEngine_Object__op_Inequality(v11, 0LL, 0LL);
      if ( (n4 & 1) != 0 )
      {
        if ( !*v10 )
          goto LABEL_236;
        n4 = *(_QWORD *)(*v10 + 552);
        if ( !n4 )
          goto LABEL_236;

        // 获取物品ID并进行特殊物品检查
        id = ItemAsset__get_id(n4, 0LL);
        HIDWORD(v106) = id;

        // 检查是否为特殊物品ID（可能是稀有物品或特殊掉落）
        if ( id != 267903 && id != 323255 && id != 960288 )
        {
          // 发送警告 - 检测到非法物品掉落
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !byte_490BC66 )
          {
            sub_184E408(&SFSController_TypeInfo);
            byte_490BC66 = 1;
          }
          SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
          }
          v14 = *SFSController_TypeInfo_3[23];

          // 构建警告消息：物品ID + 敌人ID
          n4 = System_Int32__ToString((char *)&v106 + 4, 0LL);  // 物品ID转字符串
          if ( !*v10 )
            goto LABEL_236;
          n4_9 = n4;
          LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);  // 获取敌人ID
          v16 = System_Int32__ToString(&v106, 0LL);      // 敌人ID转字符串
          n4 = System_String__Concat_60662184(StringLiteral_3357, n4_9, StringLiteral_299, v16, 0LL);
          if ( !v14 )
            goto LABEL_236;
          // 发送警告到服务器
          SFSController__SendWarn(v14, n4, StringLiteral_1, 0LL);
        }

        // 获取游戏控制器实例，准备执行掉落
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        n4 = GameController__get_instance(0LL);
        if ( !n4 )
          goto LABEL_236;
        if ( !*(_QWORD *)(a1 + 136) )  // 检查敌人Transform组件
          goto LABEL_236;
        v17 = *(_QWORD *)(n4 + 104);   // 获取掉落管理器

        // 获取敌人死亡位置
        position = UnityEngine_Transform__get_position(*(_QWORD *)(a1 + 136), 0LL);
        v21 = *v10;
        if ( !*v10 )
          goto LABEL_236;
        v22 = *(_QWORD *)(v21 + 552);  // 获取物品资产
        if ( !v22 )
          goto LABEL_236;

        // 保存位置坐标
        position_1 = position;
        v24 = v19;
        v25 = v20;

        // 获取物品掉落数量
        Quantity = ItemAsset__get_Quantity(*(_QWORD *)(v21 + 552), 0LL);

        // 获取敌人等级
        v27 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                *(_QWORD *)(a1 + 248),
                *(_QWORD *)(a1 + 256),
                0LL);

        // 检查是否为本地掉落模式
        if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                *(_QWORD *)(a1 + 428),  // 本地掉落标志
                *(unsigned int *)(a1 + 436),
                0LL) & 1) != 0 )
        {
          v28 = 1;  // 本地掉落
        }
        else
        {
          n4 = *v10;
          if ( !*v10 )
            goto LABEL_236;
          v28 = UnitAsset__get_localDropOnly(n4, 0LL) & 1;  // 检查单位是否仅本地掉落
        }
        n4 = *v10;
        if ( !*v10 || (n4 = UnitAsset__get_id(n4, 0LL), !v17) )
LABEL_236:
          sub_184E634(n4);

        // 执行物品掉落 - 在敌人死亡位置生成掉落物品
        n4 = DropManager__DropItem(v17, 0LL, 0LL, v22, Quantity, v27, v28, 0LL, position_1, v24, v25, n4, 0, 0LL);
      }

      // 检查敌人是否有掉落表（loot table）
      if ( !*v10 )
        goto LABEL_236;
      v29 = *(_QWORD *)(*v10 + 544);  // 获取掉落表
      if ( !v29 )
        goto LABEL_236;
      if ( !*(_QWORD *)(v29 + 24) )   // 检查掉落表是否为空
        return n4;

      // 初始化SFS控制器用于掉落验证
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      v30 = off_490B000;
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
      }
      n4 = *SFSController_TypeInfo_4[23];
      if ( !n4 )
        goto LABEL_236;

      // 检查是否为SFS专用掉落单位（服务器验证掉落）
      if ( (SFSController__get_SFSDropOnlyUnit(n4, 0LL) & 1) != 0 )
      {
        // 检查敌人等级限制 - 4级和5级敌人需要特殊处理
        n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
        if ( (_DWORD)n4 == 4 )
          return n4;
        n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
        if ( (_DWORD)n4 == 5 )
          return n4;
        n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
               *(_QWORD *)(a1 + 248),
               *(_QWORD *)(a1 + 256),
               0LL);
        // 6级以上敌人不允许掉落
        if ( (int)n4 > 6 )
          return n4;
      }
      // 计算随机掉落数量
      n4 = *v10;
      if ( !*v10 )
        goto LABEL_236;
      n4 = UnitAsset__get_minLoot(n4, 0LL);  // 获取最小掉落数量
      if ( !*v10 )
        goto LABEL_236;
      n4_1 = n4;
      maxLoot = UnitAsset__get_maxLoot(*v10, 0LL);  // 获取最大掉落数量

      // 在最小和最大掉落数量之间随机选择
      n4 = UnityEngine_Random__Range_69543328(n4_1, (unsigned int)(maxLoot + 1), 0LL);
      HIDWORD(n9) = n4;  // 保存掉落数量

      if ( !*v10 )
        goto LABEL_236;
      n4_2 = (unsigned int)n4;
      n4 = *(_QWORD *)(*v10 + 576);  // 获取掉落管理器
      if ( !n4 )
        goto LABEL_236;

      // 设置掉落数量
      PJDGMFMMKID__CFEEKELBNMC(n4, n4_2, 0LL);

      // 反作弊检查：掉落数量不能超过8个（防止刷物品）
      if ( SHIDWORD(n9) >= 9 )
      {
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        if ( !byte_490BC66 )
        {
          sub_184E408(&SFSController_TypeInfo);
          byte_490BC66 = 1;
        }
        SFSController_TypeInfo_5 = (_QWORD **)SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_5 = (_QWORD **)SFSController_TypeInfo;
        }
        v36 = *SFSController_TypeInfo_5[23];
        v37 = System_Int32__ToString((char *)&n9 + 4, 0LL);
        n4 = System_String__Concat_60610504(StringLiteral_17568, v37, 0LL);
        if ( !v36 )
          goto LABEL_236;

        // 记录作弊行为：掉落数量过多
        SFSController__LogHack(v36, n4, 0LL);

        if ( !byte_490BC66 )
        {
          sub_184E408(&SFSController_TypeInfo);
          byte_490BC66 = 1;
        }
        SFSController_TypeInfo_6 = (_QWORD **)SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_6 = (_QWORD **)SFSController_TypeInfo;
        }
        n4 = *SFSController_TypeInfo_6[23];
        if ( !n4 )
          goto LABEL_236;
        n4 = SFSController__get_Connected(n4, 0LL);

        // 如果未连接服务器，将掉落数量重置为1
        if ( (n4 & 1) == 0 )
        {
          HIDWORD(n9) = 1;  // 重置掉落数量为1
          if ( !*v10 )
            goto LABEL_236;
          n4 = *(_QWORD *)(*v10 + 576);
          if ( !n4 )
            goto LABEL_236;
          PJDGMFMMKID__CFEEKELBNMC(n4, 1LL, 0LL);  // 应用重置的掉落数量
        }
      }
      // 检查网络状态和Boss掉落权限
      if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
      n4 = NetworkManager__get_IsMasterClient(0LL);  // 检查是否为主客户端
      if ( (n4 & 1) != 0 )
      {
        n4 = *v10;
        if ( !*v10 )
          goto LABEL_236;
        n4 = UnitAsset__get_Boss(n4, 0LL);  // 检查是否为Boss敌人
        if ( (n4 & 1) != 0 )  // 如果是Boss
        {
          // 检查是否为本地掉落模式
          n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                 *(_QWORD *)(a1 + 428),
                 *(unsigned int *)(a1 + 436),
                 0LL);
          if ( (n4 & 1) == 0 )  // 如果不是本地掉落
          {
            n4 = *v10;
            if ( !*v10 )
              goto LABEL_236;
            n4 = UnitAsset__get_localDropOnly(n4, 0LL);  // 检查是否仅本地掉落
            if ( (n4 & 1) == 0 )  // 如果不是仅本地掉落
            {
              // Boss掉落需要服务器验证
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              if ( !byte_490BC66 )
              {
                sub_184E408(&SFSController_TypeInfo);
                byte_490BC66 = 1;
              }
              n4 = (__int64)SFSController_TypeInfo;
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              {
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                n4 = (__int64)SFSController_TypeInfo;
              }
              if ( *v10 )
              {
                v100 = **(_QWORD **)(n4 + 184);
                LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
                v101 = System_Int32__ToString(&v106, 0LL);
                n4 = System_String__Concat_60610504(StringLiteral_3894, v101, 0LL);
                if ( v100 )
                  // 发送Boss掉落警告到服务器
                  return SFSController__SendWarn(v100, n4, StringLiteral_1, 0LL);
              }
              goto LABEL_236;
            }
          }
        }
      }
      // 初始化掉落物品计数器
      LODWORD(n9) = 0;
      if ( !*v10 )
        goto LABEL_236;

      // 获取掉落表并开始遍历
      n4 = *(_QWORD *)(*v10 + 576);  // 获取掉落表
      if ( !n4 )
        goto LABEL_236;

      // 获取掉落表的枚举器
      n4 = (*(__int64 (__fastcall **)(__int64, _QWORD))(*(_QWORD *)n4 + 3608LL))(
             n4,
             *(_QWORD *)(*(_QWORD *)n4 + 3616LL));
      if ( !n4 )
        goto LABEL_236;

      // 设置枚举器接口
      v39 = *(_QWORD *)n4;
      n4_3 = n4;
      v41 = *(unsigned __int16 *)(*(_QWORD *)n4 + 302LL);
      if ( *(_WORD *)(*(_QWORD *)n4 + 302LL) )
      {
        v42 = (int *)(*(_QWORD *)(v39 + 176) + 8LL);
        while ( *((_QWORD *)v42 - 1) != System_Collections_Generic_IEnumerable_IEPDBNJKNLM__TypeInfo )
        {
          --v41;
          v42 += 4;
          if ( !v41 )
            goto LABEL_111;
        }
        v43 = v39 + 16LL * *v42 + 312;
      }
      else
      {
LABEL_111:
        v43 = sub_185BD18(n4, System_Collections_Generic_IEnumerable_IEPDBNJKNLM__TypeInfo, 0LL);
      }

      // 获取枚举器实例
      v44 = (*(__int64 (__fastcall **)(__int64, _QWORD))v43)(n4_3, *(_QWORD *)(v43 + 8));

      // 更新服务器统计：怪物掉落计数
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      n4 = (__int64)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        n4 = (__int64)SFSController_TypeInfo;
      }
      v45 = *(__int64 **)(n4 + 184);
      v46 = *v45;
      if ( !*v45 )
        goto LABEL_236;

      // 增加怪物掉落统计计数
      LODWORD(v106) = SFSController__get_MobsDrop(*v45, 0LL);
      n4 = SFSController__set_MobsDrop(v46, (int)v106 + 1);
      if ( !v44 )
        goto LABEL_236;
      // 主掉落循环：遍历掉落表中的每个物品
      while ( 1 )
      {
        while ( 1 )
        {
          while ( 1 )
          {
            // 获取枚举器接口
            v47 = *(_QWORD *)v44;
            v48 = *(unsigned __int16 *)(*(_QWORD *)v44 + 302LL);
            if ( *(_WORD *)(*(_QWORD *)v44 + 302LL) )
            {
              v49 = (int *)(*(_QWORD *)(v47 + 176) + 8LL);
              while ( *((_UNKNOWN **)v49 - 1) != System_Collections_IEnumerator_TypeInfo )
              {
                --v48;
                v49 += 4;
                if ( !v48 )
                  goto LABEL_125;
              }
              v50 = v47 + 16LL * *v49 + 312;
            }
            else
            {
LABEL_125:
              v50 = sub_185BD18(v44, System_Collections_IEnumerator_TypeInfo, 0LL);
            }

            // 检查是否还有下一个物品
            n4 = (*(__int64 (__fastcall **)(__int64, _QWORD))v50)(v44, *(_QWORD *)(v50 + 8));
            if ( (n4 & 1) == 0 )  // 如果没有更多物品，结束循环
              return n4;

            // 获取当前物品
            v51 = *(_QWORD *)v44;
            v52 = *(unsigned __int16 *)(*(_QWORD *)v44 + 302LL);
            if ( *(_WORD *)(*(_QWORD *)v44 + 302LL) )
            {
              v53 = (int *)(*(_QWORD *)(v51 + 176) + 8LL);
              while ( *((_QWORD *)v53 - 1) != System_Collections_Generic_IEnumerator_IEPDBNJKNLM__TypeInfo )
              {
                --v52;
                v53 += 4;
                if ( !v52 )
                  goto LABEL_132;
              }
              v54 = v51 + 16LL * *v53 + 312;
            }
            else
            {
LABEL_132:
              v54 = sub_185BD18(v44, System_Collections_Generic_IEnumerator_IEPDBNJKNLM__TypeInfo, 0LL);
            }

            // 获取当前掉落物品对象
            n4 = (*(__int64 (__fastcall **)(__int64, _QWORD))v54)(v44, *(_QWORD *)(v54 + 8));
            if ( !n4 )
              goto LABEL_236;
            n4_4 = n4;

            // 验证物品类型
            v56 = *((unsigned __int8 *)MHLCNCDHBNN_TypeInfo + 304);
            if ( *(unsigned __int8 *)(*(_QWORD *)n4 + 304LL) < (unsigned int)v56 )
              goto LABEL_236;
            if ( *(_UNKNOWN **)(*(_QWORD *)(*(_QWORD *)n4 + 200LL) + 8 * v56 - 8) != MHLCNCDHBNN_TypeInfo )
              goto LABEL_236;

            // 获取物品资产
            v57 = *(_QWORD *)(n4 + 104);
            if ( !v57 )
              goto LABEL_236;
            v58 = *(_QWORD *)(v57 + 136);
            if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);

            // 检查物品资产是否有效
            if ( (UnityEngine_Object__op_Inequality(v58, 0LL, 0LL) & 1) != 0 )
            {
              n4 = *(_QWORD *)(n4_4 + 104);
              if ( !n4 )
                goto LABEL_236;

              // 检查物品是否被排除掉落
              if ( (ItemAsset__get_ExcludeDrop(n4, 0LL) & 1) == 0 )
                goto LABEL_239;  // 物品可以掉落

              // 物品被排除，检查服务器连接状态
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              if ( !*((_BYTE *)v30 + 3174) )
              {
                sub_184E408(&SFSController_TypeInfo);
                *((_BYTE *)v30 + 3174) = 1;
              }
              SFSController_TypeInfo_7 = (_QWORD **)SFSController_TypeInfo;
              if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              {
                j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                SFSController_TypeInfo_7 = (_QWORD **)SFSController_TypeInfo;
              }
              n4 = *SFSController_TypeInfo_7[23];
              if ( !n4 )
                goto LABEL_236;
              if ( (SFSController__get_Connected(n4, 0LL) & 1) != 0 )
              {
LABEL_239:
                // 检查物品是否在服务器排除列表中
                if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                  j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                if ( !*((_BYTE *)v30 + 3174) )
                {
                  sub_184E408(&SFSController_TypeInfo);
                  *((_BYTE *)v30 + 3174) = 1;
                }
                n4 = (__int64)SFSController_TypeInfo;
                if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
                {
                  j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
                  n4 = (__int64)SFSController_TypeInfo;
                }
                if ( !*(_QWORD *)(n4_4 + 104) )
                  goto LABEL_236;
                v60 = **(_QWORD **)(n4 + 184);
                n4 = ItemAsset__get_id(*(_QWORD *)(n4_4 + 104), 0LL);
                if ( !v60 )
                  goto LABEL_236;
                // 如果物品不在排除列表中，可以继续处理
                if ( (SFSController__DropExcluded(v60, (unsigned int)n4, 0LL) & 1) == 0 )
                  break;
              }
            }
          }
          // 反作弊检查：掉落物品数量不能超过8个
          if ( (int)n9 >= 9 )
          {
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            if ( !*((_BYTE *)v30 + 3174) )
            {
              sub_184E408(&SFSController_TypeInfo);
              *((_BYTE *)v30 + 3174) = 1;
            }
            SFSController_TypeInfo_8 = (_QWORD **)SFSController_TypeInfo;
            if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
              SFSController_TypeInfo_8 = (_QWORD **)SFSController_TypeInfo;
            }
            v94 = *SFSController_TypeInfo_8[23];
            n4 = System_Int32__ToString(&n9, 0LL);
            if ( *v10 )
            {
              n4_5 = n4;
              LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
              v96 = System_Int32__ToString(&v106, 0LL);
              n4 = System_String__Concat_60662184(StringLiteral_19624, n4_5, StringLiteral_299, v96, 0LL);
              if ( v94 )
              {
                // 记录作弊行为：掉落物品过多
                SFSController__LogHack(v94, n4, 0LL);
                n4 = System_Int32__ToString(&n9, 0LL);
                if ( *v10 )
                {
                  n4_6 = n4;
                  LODWORD(v106) = UnitAsset__get_id(*v10, 0LL);
                  v98 = System_Int32__ToString(&v106, 0LL);
                  v99 = System_String__Concat_60662184(StringLiteral_19624, n4_6, StringLiteral_299, v98, 0LL);
                  // 确认作弊并保存记录
                  SaveController__ConfirmHack(v99, StringLiteral_1, 0LL);
                  if ( !*((_DWORD *)UnityEngine_Application_TypeInfo + 56) )
                    j_il2cpp_runtime_class_init_0(UnityEngine_Application_TypeInfo);
                  // 严重作弊：直接退出游戏
                  return UnityEngine_Application__Quit_69396248(0LL);
                }
              }
            }
            goto LABEL_236;
          }
          // 获取当前物品ID
          n4 = *(_QWORD *)(n4_4 + 104);
          if ( !n4 )
            goto LABEL_236;
          n323345 = ItemAsset__get_id(n4, 0LL);  // 获取物品ID

          // 验证掉落是否合法
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          n4 = GameController__get_instance(0LL);
          if ( !*v10 )
            goto LABEL_236;
          n4_7 = n4;
          n4 = UnitAsset__get_id(*v10, 0LL);  // 获取敌人ID
          if ( !n4_7 )
            goto LABEL_236;

          // 验证该敌人是否可以掉落该物品
          if ( (GameController__ValidDrop(n4_7, (unsigned int)n4, n323345, 0LL) & 1) != 0 )
            break;  // 掉落合法，跳出循环继续处理

          // 掉落不合法，记录并处罚
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !*((_BYTE *)v30 + 3174) )
          {
            sub_184E408(&SFSController_TypeInfo);
            *((_BYTE *)v30 + 3174) = 1;
          }
          n4 = (__int64)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            n4 = (__int64)SFSController_TypeInfo;
          }
          if ( !*v10 )
            goto LABEL_236;
          v79 = **(_QWORD **)(n4 + 184);
          v80 = UnitAsset__get_id(*v10, 0LL);
          n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                 *(_QWORD *)(a1 + 248),
                 *(_QWORD *)(a1 + 256),
                 0LL);
          if ( !v79 )
            goto LABEL_236;

          // 发送非法掉落封禁请求到服务器
          SFSController__InvalidDropBan(v79, n323345, v80, (unsigned int)n4, 0LL);
          // 确认作弊行为
          SaveController__ConfirmHack(StringLiteral_6372, StringLiteral_1, 0LL);

          // 设置作弊标志
          v81 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(1LL, 0LL);
          v83 = v82;
          GameController_TypeInfo = GameController_TypeInfo;
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            GameController_TypeInfo = GameController_TypeInfo;
          }
          v85 = GameController_TypeInfo[23];
          *(_QWORD *)(v85 + 12) = v81;
          *(_DWORD *)(v85 + 20) = v83;
          v86 = &StringLiteral_7865;
LABEL_209:
          // 延迟3秒后执行某个操作（可能是踢出玩家）
          UnityEngine_MonoBehaviour__Invoke(a1, *v86, 0LL, 3.0);
        }
        // 检查敌人是否可以掉落该物品（额外验证）
        n4 = *v10;
        if ( !*v10 )
          goto LABEL_236;
        v63 = UnitAsset__LBINHPGOKNK(n4, n323345, 0LL);  // 验证掉落权限

        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        if ( !*((_BYTE *)v30 + 3174) )
        {
          sub_184E408(&SFSController_TypeInfo);
          *((_BYTE *)v30 + 3174) = 1;
        }
        n4 = (__int64)SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          n4 = (__int64)SFSController_TypeInfo;
        }
        v64 = *(_QWORD **)(n4 + 184);
        v65 = *v64;

        // 如果掉落权限验证失败
        if ( (v63 & 1) == 0 )
        {
          n4 = *v10;
          if ( !*v10 )
            goto LABEL_236;
          v87 = UnitAsset__get_id(n4, 0LL);
          n4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                 *(_QWORD *)(a1 + 248),
                 *(_QWORD *)(a1 + 256),
                 0LL);
          if ( !v65 )
            goto LABEL_236;

          // 发送非法掉落封禁请求
          SFSController__InvalidDropBan(v65, n323345, v87, (unsigned int)n4, 0LL);
          SaveController__ConfirmHack(StringLiteral_19626, StringLiteral_1, 0LL);
          v88 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(1LL, 0LL);
          GameController_TypeInfo_1 = GameController_TypeInfo;
          v91 = v90;
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
            GameController_TypeInfo_1 = GameController_TypeInfo;
          }
          v92 = GameController_TypeInfo_1[23];
          *(_QWORD *)(v92 + 12) = v88;
          *(_DWORD *)(v92 + 20) = v91;
          v86 = &StringLiteral_3749;
          goto LABEL_209;
        }

        if ( !v65 )
          goto LABEL_236;

        // 检查服务器连接状态或特殊物品ID
        if ( (SFSController__get_Connected(*v64, 0LL) & 1) != 0 || n323345 != -25801 && n323345 != 323345 )
        {
          // 增加掉落物品计数
          LODWORD(n9) = n9 + 1;

          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          if ( !*((_BYTE *)v30 + 3174) )
          {
            sub_184E408(&SFSController_TypeInfo);
            *((_BYTE *)v30 + 3174) = 1;
          }
          n4 = (__int64)SFSController_TypeInfo;
          if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
          {
            j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
            n4 = (__int64)SFSController_TypeInfo;
          }
          v66 = *(_QWORD **)(n4 + 184);
          v67 = *v66;
          if ( !*v66 )
            goto LABEL_236;

          // 更新服务器统计：物品掉落计数
          LODWORD(v106) = SFSController__get_ItemsDroppedCount(*v66, 0LL);
          SFSController__set_ItemsDroppedCount(v67, (unsigned int)(v106 + 1), 0LL);

          // 获取游戏控制器和掉落管理器
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          n4 = GameController__get_instance(0LL);
          if ( !n4 )
            goto LABEL_236;
          if ( !*(_QWORD *)(a1 + 136) )
            goto LABEL_236;
          v68 = *(_QWORD *)(n4 + 104);

          // 获取掉落位置
          v69 = UnityEngine_Transform__get_position(*(_QWORD *)(a1 + 136), 0LL);
          v71 = v70;
          v73 = v72;

          // 生成随机掉落偏移
          v74 = UnityEngine_Random__Range_69543328(4294967146LL, 150LL, 0LL);  // X偏移
          n4 = UnityEngine_Random__Range_69543328(200LL, 250LL, 0LL);         // Z偏移

          v75 = *(_QWORD *)(n4_4 + 104);
          if ( !v75 )
            goto LABEL_236;
          n4_8 = n4;
          v77 = ItemAsset__get_Quantity(v75, 0LL);  // 获取物品数量

          // 获取敌人等级
          v104 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
                   *(_QWORD *)(a1 + 248),
                   *(_QWORD *)(a1 + 256),
                   0LL);

          // 检查掉落模式
          v105 = v105 & 0xFFFFFFFF00000000LL | *(unsigned int *)(a1 + 436);
          if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(*(_QWORD *)(a1 + 428), v105, 0LL) & 1) != 0 )
          {
            LODWORD(v78) = 1;  // 本地掉落
          }
          else
          {
            n4 = *v10;
            if ( !*v10 )
              goto LABEL_236;
            v78 = UnitAsset__get_localDropOnly(n4, 0LL) & 1;  // 检查是否仅本地掉落
          }
          n4 = *v10;
          v103 = v78;
          if ( !*v10 )
            goto LABEL_236;
          n4 = UnitAsset__get_id(n4, 0LL);
          if ( !v68 )
            goto LABEL_236;

          // 执行物品掉落 - 在指定位置生成物品
          LOBYTE(v102) = 0;
          DropManager__DropItem(v68, v74, n4_8, v75, v77, v104, v103, 0LL, v69, v71, v73, n4, v102, 0LL);
          v30 = off_490B000;
        }
      }
    }
  }
  return n4;  // 返回处理结果
}