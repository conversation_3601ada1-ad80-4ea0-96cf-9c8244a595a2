__int64 __fastcall SFSController__DNIBMLBAGDM(__int64 a1, char a2)
{
  __int64 v4; // x8
  __int64 result; // x0
  int v6; // w1

  v4 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(0LL, 0LL);
  result = *(_QWORD *)(a1 + 64);
  *(_QWORD *)(a1 + 752) = v4;
  *(_DWORD *)(a1 + 760) = v6;
  if ( result )
  {
    result = Sfs2X_SmartFox__RemoveAllEventListeners(result, 0LL);
    if ( (a2 & 1) != 0 )
      *(_QWORD *)(a1 + 64) = 0LL;
  }
  return result;
}