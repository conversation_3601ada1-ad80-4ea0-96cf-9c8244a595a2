/**
 * 玩家伤害处理函数 - 包含大量反作弊检测逻辑
 * @param a1: PlayerController实例指针
 * @param n10000: 伤害值
 * @param a3: 伤害来源标识符
 * @return: 处理结果 (0=正常处理, 1=异常/作弊检测到)
 */
__int64 __fastcall PlayerController__OverrideDamage(__int64 a1, int n10000, int a3)
{
  // 各种类型信息指针，用于Unity IL2CPP运行时系统
  _QWORD **SFSController_TypeInfo_1; // x0
  __int64 v7; // x21
  __int64 v8; // x0
  __int64 instance; // x0
  _QWORD **SFSController_TypeInfo; // x0
  __int64 v11; // x21
  __int64 v12; // x0
  __int64 *v13; // x24 - 玩家数据指针
  char isMine; // w0 - 是否为本地玩家标志
  _QWORD **SFSController_TypeInfo_5; // x0
  __int64 v16; // x20
  __int64 v17; // x1
  __int64 v18; // x0
  __int64 v19; // x1
  _QWORD **SFSController_TypeInfo_2; // x0
  _QWORD **SFSController_TypeInfo_3; // x0
  __int64 v22; // x20
  __int64 instance_1; // x21
  unsigned int n2; // w8
  unsigned int n3; // w8
  __int64 v26; // x9
  unsigned int n5; // w8
  unsigned int n7; // w8
  unsigned int n9; // w8
  unsigned int n0xB; // w8
  unsigned int n0xD; // w8
  _QWORD **SFSController_TypeInfo_4; // x0
  __int64 v34; // x1
  __int64 v35; // x0
  _QWORD *NetworkManager_TypeInfo; // x0
  __int64 v37; // x0
  _QWORD *NetworkManager_TypeInfo_1; // x8
  int v39; // w0
  __int64 v40; // x0
  __int64 v41; // x8
  __int64 v42; // x1
  _QWORD **SFSController_TypeInfo_6; // x0
  __int64 v44; // x0
  _QWORD **SFSController_TypeInfo_7; // x0
  unsigned int n10000_3; // w21
  __int64 v47; // x23
  __int64 v48; // x0
  __int64 v49; // x0
  __int64 v50; // x8
  __int64 v51; // x0
  __int64 v52; // x0
  __int64 v53; // x8
  __int64 v54; // x20
  __int64 v55; // x0
  __int64 v56; // x0
  __int64 v57; // x0
  _QWORD **SFSController_TypeInfo_8; // x0
  __int64 v59; // x20
  __int64 v60; // x1
  __int64 v61; // x0
  __int64 v62; // x1
  int v63; // w20
  _QWORD **SFSController_TypeInfo_9; // x0
  __int64 v65; // x21
  __int64 v66; // x3
  __int64 v67; // x0
  _QWORD **SFSController_TypeInfo_10; // x0
  __int64 v69; // x21
  __int64 v70; // x3
  __int64 v71; // x0
  __int64 v72; // x0
  __int64 v73; // x8
  __int64 v74; // x0
  _QWORD **SFSController_TypeInfo_11; // x0
  __int64 v76; // x0
  __int64 v77; // x21
  __int64 v78; // x0
  unsigned int n2_1; // w8
  __int64 v80; // x0
  unsigned int n3_1; // w8
  __int64 v82; // x0
  __int64 v83; // x0
  _QWORD **SFSController_TypeInfo_12; // x0
  __int64 v85; // x21
  __int64 v86; // x3
  __int64 v87; // x0
  signed int v88; // w20
  _QWORD **SFSController_TypeInfo_13; // x0
  __int64 v90; // x20
  __int64 v91; // x1
  __int64 v92; // x0
  _QWORD **SFSController_TypeInfo_14; // x0
  __int64 v94; // x20
  __int64 v95; // x0
  __int64 v96; // x21
  __int64 v97; // x0
  unsigned int n2_2; // w8
  __int64 v99; // x0
  unsigned int n3_2; // w8
  __int64 v101; // x0
  __int64 v102; // x0
  __int64 v103; // x0
  __int64 v104; // x20
  __int64 v105; // x0
  unsigned int n2_3; // w8
  __int64 v107; // x0
  unsigned int n3_3; // w8
  __int64 v109; // x0
  __int64 v110; // x0
  _QWORD **SFSController_TypeInfo_15; // x0
  __int64 v112; // x20
  __int64 v113; // x21
  __int64 v114; // x3
  __int64 v115; // x0
  int v116; // w0
  _QWORD *NetworkManager_TypeInfo_2; // x0
  __int64 v118; // x0
  __int64 v119; // x8
  int v120; // w0
  _QWORD **SFSController_TypeInfo_16; // x0
  __int64 v122; // x20
  __int64 v123; // x0
  __int64 v124; // x21
  __int64 v125; // x0
  unsigned int n2_4; // w8
  __int64 v127; // x0
  unsigned int n3_4; // w8
  __int64 v129; // x9
  __int64 v130; // x0
  unsigned int n5_1; // w8
  __int64 v132; // x0
  __int64 v133; // x0
  __int64 v134; // x20
  __int64 v135; // x0
  _QWORD **SFSController_TypeInfo_17; // x0
  __int64 v137; // x19
  __int64 v138; // x0
  __int64 v139; // x8
  __int64 v140; // x0

  // 局部变量定义
  _BYTE v141[4]; // [xsp+4h] [xbp-8Ch] BYREF - 布尔值临时存储
  __int64 v142; // [xsp+8h] [xbp-88h] BYREF - 通用临时变量
  __int64 v143; // [xsp+10h] [xbp-80h] BYREF - ObscuredBool相关
  int v144; // [xsp+18h] [xbp-78h] - ObscuredBool的key
  char v145; // [xsp+20h] [xbp-70h] - 玩家状态标志
  signed int v146; // [xsp+24h] [xbp-6Ch] BYREF - 玩家当前血量
  unsigned int n10000_2; // [xsp+28h] [xbp-68h] BYREF - 原始伤害值备份
  signed int n10000_1; // [xsp+2Ch] [xbp-64h] BYREF - 处理后的伤害值

  // 初始化伤害值
  n10000_1 = n10000;

  // 静态初始化检查 - 确保所有类型信息已加载（Unity IL2CPP运行时要求）
  if ( (byte_490E3D9 & 1) == 0 )
  {
    // 初始化各种类型信息和字符串字面量
    sub_184E408(&bool_TypeInfo);
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&NetworkManager_TypeInfo);
    sub_184E408(&UnityEngine_Object_TypeInfo);
    sub_184E408(&SFSController_TypeInfo);
    sub_184E408(&string___TypeInfo);
    sub_184E408(&StringLiteral_225);
    sub_184E408(&StringLiteral_19171);
    sub_184E408(&StringLiteral_3324);
    sub_184E408(&StringLiteral_7865);
    sub_184E408(&StringLiteral_334);
    sub_184E408(&StringLiteral_204);
    sub_184E408(&StringLiteral_217);
    sub_184E408(&StringLiteral_3823);
    sub_184E408(&StringLiteral_431);
    sub_184E408(&StringLiteral_6640);
    sub_184E408(&StringLiteral_3323);
    sub_184E408(&StringLiteral_3824);
    sub_184E408(&StringLiteral_291);
    sub_184E408(&StringLiteral_255);
    sub_184E408(&StringLiteral_238);
    sub_184E408(&StringLiteral_1902);
    sub_184E408(&StringLiteral_19177);
    sub_184E408(&StringLiteral_8310);
    sub_184E408(&StringLiteral_14348);
    sub_184E408(&StringLiteral_353);
    sub_184E408(&StringLiteral_368);
    sub_184E408(&StringLiteral_370);
    sub_184E408(&StringLiteral_6807);
    sub_184E408(&StringLiteral_3325);
    sub_184E408(&StringLiteral_462);
    sub_184E408(&StringLiteral_260);
    sub_184E408(&StringLiteral_1);
    sub_184E408(&StringLiteral_258);
    sub_184E408(&StringLiteral_520);
    sub_184E408(&StringLiteral_3337);
    sub_184E408(&StringLiteral_10078);
    sub_184E408(&StringLiteral_8657);
    byte_490E3D9 = 1; // 标记已初始化
  }

  // 初始化局部变量
  v146 = 0;           // 玩家当前血量
  n10000_2 = n10000;  // 备份原始伤害值
  v145 = 0;           // 玩家状态标志
  v144 = 0;           // ObscuredBool的key
  v142 = 0LL;         // 通用临时变量
  v143 = 0LL;         // ObscuredBool相关
  v141[0] = 0;        // 布尔值临时存储

  // 【反作弊检测1】检查伤害值是否为负数
  if ( n10000 < 0 )
  {
    // 获取SFSController实例用于发送警告
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo = (_QWORD **)SFSController_TypeInfo;
    }
    v11 = *SFSController_TypeInfo[23];
    v12 = System_Int32__ToString(&n10000_1, 0LL);
    instance = System_String__Concat_60610504(StringLiteral_3824, v12, 0LL);
    if ( !v11 )
      goto LABEL_395;
    // 发送作弊警告：负伤害值
    SFSController__SendWarn(v11, instance, StringLiteral_1, 0LL);
    // 将负伤害值修正为随机正值(1-11)
    n10000_1 = UnityEngine_Random__Range_69543328(1LL, 12LL, 0LL);
  }
  // 【反作弊检测2】检查伤害值是否过大(>=10000)
  else if ( n10000 >= 10000 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_1 = (_QWORD **)SFSController_TypeInfo;
    }
    v7 = *SFSController_TypeInfo_1[23];
    v8 = System_Int32__ToString(&n10000_1, 0LL);
    instance = System_String__Concat_60610504(StringLiteral_3823, v8, 0LL);
    if ( !v7 )
      goto LABEL_395;
    // 发送作弊警告：伤害值过大
    SFSController__SendWarn(v7, instance, StringLiteral_1, 0LL);
    // 将过大伤害值限制为9999
    n10000_1 = 9999;
  }
  // 【关键检测】检查玩家数据是否存在
  if ( !*(_QWORD *)(a1 + 344) )
  {
    // 玩家数据不存在，确认为作弊行为
    SaveController__ConfirmHack(StringLiteral_19177, StringLiteral_1, 0LL);
    SaveController__LockDown(0LL); // 锁定游戏
    // 延迟3秒后执行某个操作（可能是踢出玩家）
    UnityEngine_MonoBehaviour__Invoke(a1, StringLiteral_7865, 0LL, 3.0);
    return 1LL; // 返回1表示检测到作弊
  }

  // 获取PhotonView实例（网络同步组件）
  instance = *(_QWORD *)(a1 + 304);
  if ( !instance )
    goto LABEL_395;

  // 获取玩家数据指针
  v13 = (__int64 *)(a1 + 344);

  // 【网络权限检测】检查是否为本地玩家
  isMine = PhotonView__get_isMine(instance, 0LL);

  // 如果伤害来源不是-1且不是本地玩家，跳转到权限检查失败处理
  if ( a3 != -1 && (isMine & 1) == 0 )
    goto LABEL_399;

  // 获取玩家数据实例
  instance = *v13;
  if ( !*v13 )
    goto LABEL_395;

  // 【玩家状态检测】检查玩家是否处于可受伤状态
  if ( (AIPPAPNPBNB__BNJMNLCJGDJ(instance, 0LL) & 1) == 0 )
  {
LABEL_399:
    // 权限检查失败或玩家状态异常的处理
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_2 = (_QWORD **)SFSController_TypeInfo;
    }
    instance = *SFSController_TypeInfo_2[23];
    if ( !instance )
      goto LABEL_395;

    // 如果启用了受击日志记录
    if ( (SFSController__get_LogTakeHit(instance, 0LL) & 1) == 0 )
      goto LABEL_81;

    // 再次检查是否为本地玩家
    instance = *(_QWORD *)(a1 + 304);
    if ( !instance )
      goto LABEL_395;
    if ( (PhotonView__get_isMine(instance, 0LL) & 1) == 0 )
      goto LABEL_81;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_3 = (_QWORD **)SFSController_TypeInfo;
    }
    v22 = *SFSController_TypeInfo_3[23];
    instance = sub_184E480(string___TypeInfo, 16LL);
    if ( instance )
    {
      instance_1 = instance;
      if ( !*(_DWORD *)(instance + 24) )
        goto LABEL_396;
      *(_QWORD *)(instance + 32) = StringLiteral_6807;
      instance = System_Int32__ToString(&n10000_1, 0LL);
      n2 = *(_DWORD *)(instance_1 + 24);
      if ( n2 <= 1 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 40) = instance;
      if ( n2 == 2 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 48) = StringLiteral_520;
      instance = *v13;
      if ( !*v13 )
        goto LABEL_395;
      v141[0] = AIPPAPNPBNB__BNJMNLCJGDJ(instance, 0LL) & 1;
      if ( !*((_DWORD *)bool_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(bool_TypeInfo);
      instance = System_Boolean__ToString(v141, 0LL);
      n3 = *(_DWORD *)(instance_1 + 24);
      if ( n3 <= 3 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 56) = instance;
      if ( n3 == 4 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 64) = StringLiteral_370;
      v26 = *(_QWORD *)(a1 + 360);
      v144 = *(_DWORD *)(a1 + 368);
      v143 = v26;
      instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__ToString(&v143, 0LL);
      n5 = *(_DWORD *)(instance_1 + 24);
      if ( n5 <= 5 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 72) = instance;
      if ( n5 == 6 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 80) = StringLiteral_238;
      instance = *v13;
      if ( !*v13 )
        goto LABEL_395;
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(instance, 0LL);
      instance = System_Int32__ToString(&v142, 0LL);
      n7 = *(_DWORD *)(instance_1 + 24);
      if ( n7 <= 7 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 88) = instance;
      if ( n7 == 8 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 96) = StringLiteral_462;
      instance = *v13;
      if ( !*v13 )
        goto LABEL_395;
      LODWORD(v142) = AIPPAPNPBNB__GMFCHHKIDNF(instance, 0LL);
      instance = System_Int32__ToString(&v142, 0LL);
      n9 = *(_DWORD *)(instance_1 + 24);
      if ( n9 <= 9 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 104) = instance;
      if ( n9 == 10 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 112) = StringLiteral_225;
      instance = System_Int32__ToString(&n10000_1, 0LL);
      n0xB = *(_DWORD *)(instance_1 + 24);
      if ( n0xB <= 0xB )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 120) = instance;
      if ( n0xB == 12 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 128) = StringLiteral_431;
      instance = System_Int32__ToString(&n10000_2, 0LL);
      n0xD = *(_DWORD *)(instance_1 + 24);
      if ( n0xD <= 0xD )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 136) = instance;
      if ( n0xD == 14 )
        goto LABEL_396;
      *(_QWORD *)(instance_1 + 144) = StringLiteral_258;
      if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
      v141[0] = DCEBNDFOAMB__HNMKFNACEOP(0LL) & 1;
      instance = System_Boolean__ToString(v141, 0LL);
      if ( *(_DWORD *)(instance_1 + 24) <= 0xFu )
LABEL_396:
        sub_184E63C(instance);
      *(_QWORD *)(instance_1 + 152) = instance;
      instance = System_String__Concat_60662448(instance_1, 0LL);
      if ( v22 )
      {
        SFSController__SendWarn(v22, instance, StringLiteral_1, 0LL);
LABEL_81:
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        instance = GameController__get_instance(0LL);
        if ( instance )
        {
          instance = *(_QWORD *)(instance + 88);
          if ( instance )
          {
            SoundController__PlaySfx(instance, 9LL, 0LL);
            PlayerController__AICBBLFEBPP(a1, (unsigned int)n10000_1);
            instance = *(_QWORD *)(a1 + 208);
            if ( instance )
            {
              PlayerSprite__Flash(instance, 0LL);
              PlayerController__HDJBCANKKFP(a1);
              return 1LL;
            }
          }
        }
      }
    }
LABEL_395:
    sub_184E634(instance);
  }
  // 【伤害计算】通过某种算法处理伤害值（可能包含护甲计算等）
  // 💡 减伤方法1：在调用伤害计算前先减少伤害值
  // n10000_1 = n10000_1 / 2;  // 减少50%伤害
  // n10000_1 = n10000_1 * 0.8; // 减少20%伤害
  // if (n10000_1 > 10) n10000_1 = 10; // 限制最大伤害为10

  n10000_1 = PlayerController__MIDGHMNDKGI(a1, (unsigned int)n10000_1);

  // 💡 减伤方法2：在伤害计算后再次减少
  // n10000_1 = n10000_1 / 2;  // 进一步减少伤害

  // 重置状态变量
  v146 = 0;
  v145 = 0;

  // 获取玩家当前血量
  if ( !*v13 )
    sub_184E634(0LL);
  v146 = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);

  // 获取玩家某种状态标志
  if ( !*v13 )
    sub_184E634(0LL);
  v145 = AIPPAPNPBNB__NCGLLEALBHO() & 1;

  // 【二次伤害值检测】处理后的伤害值仍需检查
  if ( n10000_1 <= 0 )
  {
    // 处理后伤害值仍为负数或0，发送警告
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_4 = (_QWORD **)SFSController_TypeInfo;
    }
    v16 = *SFSController_TypeInfo_4[23];
    v34 = System_Int32__ToString(&n10000_1, 0LL);
    v35 = System_String__Concat_60610504(StringLiteral_3324, v34, 0LL);
    v19 = v35;
    if ( !v16 )
      sub_184E634(v35);
  }
  else
  {
    // 如果伤害值在正常范围内，跳过警告
    if ( n10000_1 < 10000 )
      goto LABEL_97;

    // 处理后伤害值仍过大，发送警告
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_5 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_5 = (_QWORD **)SFSController_TypeInfo;
    }
    v16 = *SFSController_TypeInfo_5[23];
    v17 = System_Int32__ToString(&n10000_1, 0LL);
    v18 = System_String__Concat_60610504(StringLiteral_3323, v17, 0LL);
    v19 = v18;
    if ( !v16 )
      sub_184E634(v18);
  }
  // 发送伤害值异常警告
  SFSController__SendWarn(v16, v19, StringLiteral_1, 0LL);

LABEL_97:
  // 💡 减伤方法3：在最终应用伤害前修改
  // n10000_1 = 1; // 强制设置为最小伤害
  // n10000_1 = n10000_1 > 5 ? 5 : n10000_1; // 限制最大伤害为5

  // 【核心伤害处理】应用伤害到玩家
  PlayerController__AICBBLFEBPP(a1, (unsigned int)n10000_1);
  // 【网络统计更新】更新网络管理器中的统计数据
  NetworkManager_TypeInfo = NetworkManager_TypeInfo;
  if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
    NetworkManager_TypeInfo = NetworkManager_TypeInfo;
  }

  // 增加受击次数计数器（使用ObscuredShort防作弊）
  v37 = CodeStage_AntiCheat_ObscuredTypes_ObscuredShort__op_Increment(
          *(_QWORD *)(NetworkManager_TypeInfo[23] + 128LL),
          0LL);
  NetworkManager_TypeInfo_1 = NetworkManager_TypeInfo;
  *(_QWORD *)(*((_QWORD *)NetworkManager_TypeInfo + 23) + 128LL) = v37;

  // 累加总伤害值（使用ObscuredInt防作弊）
  v39 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
          *(_QWORD *)(NetworkManager_TypeInfo_1[23] + 136LL),
          *(_QWORD *)(NetworkManager_TypeInfo_1[23] + 144LL),
          0LL);
  v40 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit((unsigned int)(n10000_1 + v39), 0LL);
  v41 = *((_QWORD *)NetworkManager_TypeInfo + 23);
  *(_QWORD *)(v41 + 136) = v40;
  *(_QWORD *)(v41 + 144) = v42;
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
  if ( !byte_490BC66 )
  {
    sub_184E408(&SFSController_TypeInfo);
    byte_490BC66 = 1;
  }
  SFSController_TypeInfo_6 = (_QWORD **)SFSController_TypeInfo;
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    SFSController_TypeInfo_6 = (_QWORD **)SFSController_TypeInfo;
  }
  v44 = *SFSController_TypeInfo_6[23];
  if ( !v44 )
    sub_184E634(0LL);
  if ( (SFSController__get_LogTakeHit(v44, 0LL) & 1) != 0 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_7 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_7 = (_QWORD **)SFSController_TypeInfo;
    }
    if ( !*v13 )
      sub_184E634(SFSController_TypeInfo_7);
    n10000_3 = n10000_1;
    v47 = *SFSController_TypeInfo_7[23];
    v48 = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
    if ( !v47 )
      sub_184E634(v48);
    SFSController__PlayerTakeDamage_26902300(v47, n10000_2, n10000_3, v146, v48);
  }
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  v49 = GameController__get_instance(0LL);
  if ( !v49 )
    sub_184E634(0LL);
  v50 = *(_QWORD *)(v49 + 96);
  if ( !v50 )
    sub_184E634(v49);
  v51 = *(_QWORD *)(v50 + 80);
  if ( !v51 )
    sub_184E634(0LL);
  HIDWORD(v142) = UI_Bar__get_Value(v51, 0LL);
  v52 = GameController__get_instance(0LL);
  if ( !v52 )
    sub_184E634(0LL);
  v53 = *(_QWORD *)(v52 + 96);
  if ( !v53 )
    sub_184E634(v52);
  if ( !*v13 )
    sub_184E634(0LL);
  v54 = *(_QWORD *)(v53 + 80);
  v55 = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
  if ( !v54 )
    sub_184E634(v55);
  UI_Bar__UpdateValues(v54, (unsigned int)v55, 0xFFFFFFFFLL, 0LL);
  v56 = GameController__get_instance(0LL);
  if ( !v56 )
    sub_184E634(0LL);
  v57 = *(_QWORD *)(v56 + 88);
  if ( !v57 )
    sub_184E634(0LL);
  SoundController__PlaySfx(v57, 9LL, 0LL);
  // 【血量一致性检测】如果玩家处于特定状态
  if ( v145 )
  {
    if ( !*v13 )
      sub_184E634(0LL);

    // 【反作弊检测3】检查血量是否未发生变化（可能是无敌作弊）
    if ( v146 == (unsigned int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) )
    {
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_8 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_8 = (_QWORD **)SFSController_TypeInfo;
      }
      if ( !*v13 )
        sub_184E634(SFSController_TypeInfo_8);
      v59 = *SFSController_TypeInfo_8[23];
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
      v60 = System_Int32__ToString(&v142, 0LL);
      v61 = System_String__Concat_60610504(StringLiteral_14348, v60, 0LL);
      v62 = v61;
      if ( !v59 )
        sub_184E634(v61);
LABEL_196:
      // 发送血量异常警告
      SFSController__SendWarn(v59, v62, StringLiteral_1, 0LL);
      goto LABEL_197;
    }
    if ( !*v13 )
      sub_184E634(0LL);
    v63 = AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL);
    if ( !*v13 )
      sub_184E634(0LL);
    if ( v63 < (int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) )
    {
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_9 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_9 = (_QWORD **)SFSController_TypeInfo;
      }
      if ( !*v13 )
        sub_184E634(SFSController_TypeInfo_9);
      v59 = *SFSController_TypeInfo_9[23];
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
      v65 = System_Int32__ToString(&v142, 0LL);
      if ( !*v13 )
        sub_184E634(0LL);
      LODWORD(v142) = AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL);
      v66 = System_Int32__ToString(&v142, 0LL);
      v67 = System_String__Concat_60662184(StringLiteral_8310, v65, StringLiteral_260, v66, 0LL);
      v62 = v67;
      if ( !v59 )
        sub_184E634(v67);
      goto LABEL_196;
    }
    if ( !*v13 )
      sub_184E634(0LL);
    if ( (int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) > v146 )
    {
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_10 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_10 = (_QWORD **)SFSController_TypeInfo;
      }
      if ( !*v13 )
        sub_184E634(SFSController_TypeInfo_10);
      v59 = *SFSController_TypeInfo_10[23];
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
      v69 = System_Int32__ToString(&v142, 0LL);
      v70 = System_Int32__ToString(&v146, 0LL);
      v71 = System_String__Concat_60662184(StringLiteral_3325, v69, StringLiteral_204, v70, 0LL);
      v62 = v71;
      if ( !v59 )
        sub_184E634(v71);
      goto LABEL_196;
    }
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    v72 = GameController__get_instance(0LL);
    if ( !v72 )
      sub_184E634(0LL);
    v73 = *(_QWORD *)(v72 + 96);
    if ( !v73 )
      sub_184E634(v72);
    v74 = *(_QWORD *)(v73 + 80);
    if ( !v74 )
      sub_184E634(0LL);
    if ( HIDWORD(v142) == (unsigned int)UI_Bar__get_Value(v74, 0LL) )
    {
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_11 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_11 = (_QWORD **)SFSController_TypeInfo;
      }
      v59 = *SFSController_TypeInfo_11[23];
      v76 = sub_184E480(string___TypeInfo, 6LL);
      v77 = v76;
      if ( !v76 )
        sub_184E634(0LL);
      if ( !*(_DWORD *)(v76 + 24) )
        sub_184E63C(v76);
      *(_QWORD *)(v76 + 32) = StringLiteral_10078;
      if ( !*v13 )
        sub_184E634(0LL);
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
      v78 = System_Int32__ToString(&v142, 0LL);
      n2_1 = *(_DWORD *)(v77 + 24);
      if ( n2_1 <= 1 )
        sub_184E63C(v78);
      *(_QWORD *)(v77 + 40) = v78;
      if ( n2_1 == 2 )
        sub_184E63C(v78);
      *(_QWORD *)(v77 + 48) = StringLiteral_204;
      v80 = System_Int32__ToString(&v146, 0LL);
      n3_1 = *(_DWORD *)(v77 + 24);
      if ( n3_1 <= 3 )
        sub_184E63C(v80);
      *(_QWORD *)(v77 + 56) = v80;
      if ( n3_1 == 4 )
        sub_184E63C(v80);
      *(_QWORD *)(v77 + 64) = StringLiteral_291;
      v82 = System_Int32__ToString((char *)&v142 + 4, 0LL);
      if ( *(_DWORD *)(v77 + 24) <= 5u )
        sub_184E63C(v82);
      *(_QWORD *)(v77 + 72) = v82;
      v83 = System_String__Concat_60662448(v77, 0LL);
      v62 = v83;
      if ( !v59 )
        sub_184E634(v83);
      goto LABEL_196;
    }
    if ( v146 < 0 )
      goto LABEL_400;
    if ( !*v13 )
      sub_184E634(0LL);
    if ( (AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) & 0x80000000) != 0 )
    {
LABEL_400:
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_12 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_12 = (_QWORD **)SFSController_TypeInfo;
      }
      v59 = *SFSController_TypeInfo_12[23];
      v85 = System_Int32__ToString(&v146, 0LL);
      if ( !*v13 )
        sub_184E634(0LL);
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
      v86 = System_Int32__ToString(&v142, 0LL);
      v87 = System_String__Concat_60662184(StringLiteral_3337, v85, StringLiteral_217, v86, 0LL);
      v62 = v87;
      if ( !v59 )
        sub_184E634(v87);
      goto LABEL_196;
    }
  }
LABEL_197:
  v88 = v146;
  if ( v146 )
  {
    if ( !*v13 )
      sub_184E634(0LL);
    if ( v88 == (unsigned int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) )
    {
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      if ( !byte_490BC66 )
      {
        sub_184E408(&SFSController_TypeInfo);
        byte_490BC66 = 1;
      }
      SFSController_TypeInfo_13 = (_QWORD **)SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo_13 = (_QWORD **)SFSController_TypeInfo;
      }
      if ( !*v13 )
        sub_184E634(SFSController_TypeInfo_13);
      v90 = *SFSController_TypeInfo_13[23];
      LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
      v91 = System_Int32__ToString(&v142, 0LL);
      v92 = System_String__Concat_60610504(StringLiteral_14348, v91, 0LL);
      if ( !v90 )
        sub_184E634(v92);
      SFSController__SendWarn(v90, v92, StringLiteral_1, 0LL);
    }
  }
  // 【数值范围检测】检查玩家各项数值是否在合理范围内
  if ( !*v13 )
    sub_184E634(0LL);

  // 检查最大血量是否超过300
  if ( (int)AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL) > 300 )
    goto LABEL_401;
  if ( !*v13 )
    sub_184E634(0LL);

  // 检查当前血量是否超过300或伤害值小于1
  if ( (int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) > 300 || n10000_1 < 1 )
    goto LABEL_401;
  if ( !*v13 )
    sub_184E634(0LL);

  // 检查当前血量是否小于-10（异常负值）
  if ( (int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) < -10 )
    goto LABEL_401;
  if ( !*v13 )
    sub_184E634(0LL);

  // 检查最大血量是否小于等于-11（异常负值）
  if ( (int)AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL) <= -11 )
  {
LABEL_401:
    // 【严重作弊检测】数值超出合理范围，执行严厉处罚
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_14 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_14 = (_QWORD **)SFSController_TypeInfo;
    }
    v94 = *SFSController_TypeInfo_14[23];
    v95 = sub_184E480(string___TypeInfo, 6LL);
    v96 = v95;
    if ( !v95 )
      sub_184E634(0LL);
    if ( !*(_DWORD *)(v95 + 24) )
      sub_184E63C(v95);
    *(_QWORD *)(v95 + 32) = StringLiteral_19171;
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL);
    v97 = System_Int32__ToString(&v142, 0LL);
    n2_2 = *(_DWORD *)(v96 + 24);
    if ( n2_2 <= 1 )
      sub_184E63C(v97);
    *(_QWORD *)(v96 + 40) = v97;
    if ( n2_2 == 2 )
      sub_184E63C(v97);
    *(_QWORD *)(v96 + 48) = StringLiteral_353;
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
    v99 = System_Int32__ToString(&v142, 0LL);
    n3_2 = *(_DWORD *)(v96 + 24);
    if ( n3_2 <= 3 )
      sub_184E63C(v99);
    *(_QWORD *)(v96 + 56) = v99;
    if ( n3_2 == 4 )
      sub_184E63C(v99);
    *(_QWORD *)(v96 + 64) = StringLiteral_368;
    v101 = System_Int32__ToString(&n10000_1, 0LL);
    if ( *(_DWORD *)(v96 + 24) <= 5u )
      sub_184E63C(v101);
    *(_QWORD *)(v96 + 72) = v101;
    v102 = System_String__Concat_60662448(v96, 0LL);
    if ( !v94 )
      sub_184E634(v102);
    SFSController__SendWarn(v94, v102, StringLiteral_1, 0LL);
    v103 = sub_184E480(string___TypeInfo, 6LL);
    v104 = v103;
    if ( !v103 )
      sub_184E634(0LL);
    if ( !*(_DWORD *)(v103 + 24) )
      sub_184E63C(v103);
    *(_QWORD *)(v103 + 32) = StringLiteral_19171;
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL);
    v105 = System_Int32__ToString(&v142, 0LL);
    n2_3 = *(_DWORD *)(v104 + 24);
    if ( n2_3 <= 1 )
      sub_184E63C(v105);
    *(_QWORD *)(v104 + 40) = v105;
    if ( n2_3 == 2 )
      sub_184E63C(v105);
    *(_QWORD *)(v104 + 48) = StringLiteral_353;
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
    v107 = System_Int32__ToString(&v142, 0LL);
    n3_3 = *(_DWORD *)(v104 + 24);
    if ( n3_3 <= 3 )
      sub_184E63C(v107);
    *(_QWORD *)(v104 + 56) = v107;
    if ( n3_3 == 4 )
      sub_184E63C(v107);
    *(_QWORD *)(v104 + 64) = StringLiteral_368;
    v109 = System_Int32__ToString(&n10000_1, 0LL);
    if ( *(_DWORD *)(v104 + 24) <= 5u )
      sub_184E63C(v109);
    *(_QWORD *)(v104 + 72) = v109;
    v110 = System_String__Concat_60662448(v104, 0LL);
    SaveController__ConfirmHack(v110, StringLiteral_1, 0LL);
  }
  // 【高血量检测】检查是否存在异常高血量
  if ( !*v13 )
    sub_184E634(0LL);

  // 检查最大血量是否超过230
  if ( (int)AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL) > 230 )
    goto LABEL_402;
  if ( !*v13 )
    sub_184E634(0LL);

  // 检查当前血量是否大于等于231
  if ( (int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) >= 231 )
  {
LABEL_402:
    // 【高血量作弊警告】血量超过正常上限
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_15 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_15 = (_QWORD **)SFSController_TypeInfo;
    }
    if ( !*v13 )
      sub_184E634(SFSController_TypeInfo_15);
    v112 = *SFSController_TypeInfo_15[23];

    // 构建包含当前血量和最大血量的警告消息
    LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
    v113 = System_Int32__ToString(&v142, 0LL);
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL);
    v114 = System_Int32__ToString(&v142, 0LL);
    v115 = System_String__Concat_60662184(StringLiteral_6640, v113, StringLiteral_255, v114, 0LL);
    if ( !v112 )
      sub_184E634(v115);
    // 发送高血量作弊警告
    SFSController__SendWarn(v112, v115, StringLiteral_1, 0LL);
  }
  // 【玩家状态检测】获取玩家的某个布尔状态（可能是无敌状态等）
  v116 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
           *(_QWORD *)(a1 + 360),
           *(unsigned int *)(a1 + 368),
           0LL);

  // 【特殊状态处理】如果玩家不在特定状态且某个条件满足
  if ( !v145 && ((v116 ^ 1) & 1) != 0 )
  {
    // 更新网络管理器中的某个计数器
    NetworkManager_TypeInfo_2 = NetworkManager_TypeInfo;
    if ( !*((_DWORD *)NetworkManager_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(NetworkManager_TypeInfo);
      NetworkManager_TypeInfo_2 = NetworkManager_TypeInfo;
    }

    // 增加某种事件计数器（使用ObscuredByte防作弊）
    v118 = CodeStage_AntiCheat_ObscuredTypes_ObscuredByte__op_Increment(
             *(unsigned int *)(NetworkManager_TypeInfo_2[23] + 152LL) | ((unsigned __int64)*(unsigned __int8 *)(NetworkManager_TypeInfo_2[23] + 156LL) << 32),
             0LL);
    v119 = *((_QWORD *)NetworkManager_TypeInfo + 23);
    *(_BYTE *)(v119 + 156) = BYTE4(v118);
    *(_DWORD *)(v119 + 152) = v118;

    // 执行某种特殊处理
    PlayerController__DJMJGDCINMP(a1);
    return 1LL; // 提前返回，表示特殊情况处理完毕
  }
  if ( !*v13 )
    sub_184E634(0LL);
  if ( (int)AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL) < 1
    || !v145
    || (v120 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                 *(_QWORD *)(a1 + 360),
                 *(unsigned int *)(a1 + 368),
                 0LL),
        v146 < 1)
    || ((v120 ^ 1) & 1) == 0 )
  {
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    if ( !byte_490BC66 )
    {
      sub_184E408(&SFSController_TypeInfo);
      byte_490BC66 = 1;
    }
    SFSController_TypeInfo_16 = (_QWORD **)SFSController_TypeInfo;
    if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
      SFSController_TypeInfo_16 = (_QWORD **)SFSController_TypeInfo;
    }
    v122 = *SFSController_TypeInfo_16[23];
    v123 = sub_184E480(string___TypeInfo, 8LL);
    v124 = v123;
    if ( !v123 )
      sub_184E634(0LL);
    if ( !*(_DWORD *)(v123 + 24) )
      sub_184E63C(v123);
    *(_QWORD *)(v123 + 32) = StringLiteral_8657;
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__EEEKJIDCKHN(*v13, 0LL);
    v125 = System_Int32__ToString(&v142, 0LL);
    n2_4 = *(_DWORD *)(v124 + 24);
    if ( n2_4 <= 1 )
      sub_184E63C(v125);
    *(_QWORD *)(v124 + 40) = v125;
    if ( n2_4 == 2 )
      sub_184E63C(v125);
    *(_QWORD *)(v124 + 48) = StringLiteral_1902;
    if ( !*v13 )
      sub_184E634(0LL);
    LODWORD(v142) = AIPPAPNPBNB__GMFCHHKIDNF(*v13, 0LL);
    v127 = System_Int32__ToString(&v142, 0LL);
    n3_4 = *(_DWORD *)(v124 + 24);
    if ( n3_4 <= 3 )
      sub_184E63C(v127);
    *(_QWORD *)(v124 + 56) = v127;
    if ( n3_4 == 4 )
      sub_184E63C(v127);
    *(_QWORD *)(v124 + 64) = StringLiteral_370;
    v129 = *(_QWORD *)(a1 + 360);
    v144 = *(_DWORD *)(a1 + 368);
    v143 = v129;
    v130 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__ToString(&v143, 0LL);
    n5_1 = *(_DWORD *)(v124 + 24);
    if ( n5_1 <= 5 )
      sub_184E63C(v130);
    *(_QWORD *)(v124 + 72) = v130;
    if ( n5_1 == 6 )
      sub_184E63C(v130);
    *(_QWORD *)(v124 + 80) = StringLiteral_334;
    v132 = System_Int32__ToString(&v146, 0LL);
    if ( *(_DWORD *)(v124 + 24) <= 7u )
      sub_184E63C(v132);
    *(_QWORD *)(v124 + 88) = v132;
    v133 = System_String__Concat_60662448(v124, 0LL);
    if ( !v122 )
      sub_184E634(v133);
    SFSController__SendWarn(v122, v133, StringLiteral_1, 0LL);
  }
  // 【视觉效果处理】处理玩家受伤的视觉反馈
  v134 = *(_QWORD *)(a1 + 208); // 获取PlayerSprite组件
  if ( !*((_DWORD *)UnityEngine_Object_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(UnityEngine_Object_TypeInfo);

  // 如果PlayerSprite存在，播放受伤闪烁效果
  if ( (UnityEngine_Object__op_Inequality(v134, 0LL, 0LL) & 1) != 0 )
  {
    v135 = *(_QWORD *)(a1 + 208);
    if ( !v135 )
      sub_184E634(0LL);
    PlayerSprite__Flash(v135, 0LL); // 播放受伤闪烁动画
  }

  // 【网络同步】向服务器发送玩家受伤信息
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
  if ( !byte_490BC66 )
  {
    sub_184E408(&SFSController_TypeInfo);
    byte_490BC66 = 1;
  }
  SFSController_TypeInfo_17 = (_QWORD **)SFSController_TypeInfo;
  if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
    SFSController_TypeInfo_17 = (_QWORD **)SFSController_TypeInfo;
  }
  v137 = *SFSController_TypeInfo_17[23];

  // 获取游戏控制器实例
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  v138 = GameController__get_instance(0LL);
  if ( !v138 )
    sub_184E634(0LL);
  v139 = *(_QWORD *)(v138 + 112);
  if ( !v139 )
    sub_184E634(v138);

  // 获取玩家ID（使用ObscuredInt防作弊）
  v140 = CodeStage_AntiCheat_ObscuredTypes_ObscuredInt__op_Implicit_26303636(
           *(_QWORD *)(v139 + 72),
           *(_QWORD *)(v139 + 80),
           0LL);
  if ( !v137 )
    sub_184E634(v140);

  // 💡 减伤方法4：修改发送给服务器的伤害值
  // int reduced_damage = n10000_1 / 3; // 减少66%的网络同步伤害
  // int reduced_damage = 1; // 强制网络同步为最小伤害

  // 【最终网络同步】向服务器发送玩家受伤数据
  SFSController__PlayerTakeDamage(v137, v140, n10000_1);

  return 0LL; // 返回0表示正常处理完成
}